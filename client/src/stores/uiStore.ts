import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Modal types for the SahAI extension
export type ModalType =
  | 'settings'
  | 'modelConfiguration'
  | 'apiKey'
  | 'chatHistory'
  | 'providerHealth'
  | 'analytics'
  | 'help'
  | 'about';

export type AccessMethod = 'settings' | 'direct';
export type ModalVariant = 'slide-in';

interface ModalData {
  id?: string;
  name?: string;
  [key: string]: any;
}

// Basic types for SahAI V2
export interface ModelInfo {
  id: string;
  name: string;
  description?: string;
  contextLength?: number;
  inputCost?: number;
  outputCost?: number;
  capabilities?: string[];
}

export interface ProviderConfig {
  id: string;
  name: string;
  type: string;
  isConfigured: boolean;
  isOnline: boolean;
  apiKey?: string;
  baseURL?: string;
  settings: {
    temperature: number;
    maxTokens: number;
    timeout: number;
    defaultModel?: string;
  };
}

interface UIState {
  // Modal management
  currentModal: ModalType | null;
  modalData: ModalData | null;
  accessMethod: AccessMethod;
  isOpen: boolean;
  variant: ModalVariant;
  modalHistory: ModalType[];

  // Provider management
  providers: ProviderConfig[];
  currentProvider: ProviderConfig | null;

  // Model management
  availableModels: ModelInfo[];
  currentModel: ModelInfo | null;
  modelsLoading: boolean;
  modelsError: string | null;

  // UI state
  theme: 'light' | 'dark';
  sidebarCollapsed: boolean;
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    timestamp: Date;
  }>;

  // Modal actions
  openModal: (modalType: ModalType, data?: ModalData, method?: AccessMethod, variant?: ModalVariant) => void;
  openSubModal: (modalType: ModalType, data?: ModalData) => void;
  closeModal: () => void;
  goBackToSettings: () => void;

  // Provider actions
  setCurrentProvider: (providerId: string | null) => Promise<void>;
  setCurrentModel: (modelId: string | null) => void;
  updateProviderConfig: (id: string, updates: Partial<ProviderConfig>) => void;
  updateProviderApiKey: (providerId: string, apiKey: string) => Promise<void>;

  // UI actions
  setTheme: (theme: 'light' | 'dark') => void;
  toggleSidebar: () => void;
  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
}

// CEP-compatible storage implementation
const cepStorage = {
  getItem: (key: string): Promise<string | null> => {
    return new Promise((resolve) => {
      try {
        if (typeof window !== 'undefined' && (window as any).CSInterface) {
          const csInterface = new (window as any).CSInterface();
          csInterface.KVStorage.getItem(key, (data: string) => {
            resolve(data || null);
          });
        } else {
          resolve(localStorage.getItem(key));
        }
      } catch (error) {
        console.error('Error getting item from CEP storage:', error);
        resolve(null);
      }
    });
  },
  
  setItem: (key: string, value: string): Promise<void> => {
    return new Promise((resolve) => {
      try {
        if (typeof window !== 'undefined' && (window as any).CSInterface) {
          const csInterface = new (window as any).CSInterface();
          csInterface.KVStorage.setItem(key, value, () => {
            resolve();
          });
        } else {
          localStorage.setItem(key, value);
          resolve();
        }
      } catch (error) {
        console.error('Error setting item in CEP storage:', error);
        resolve();
      }
    });
  },
  
  removeItem: (key: string): Promise<void> => {
    return new Promise((resolve) => {
      try {
        if (typeof window !== 'undefined' && (window as any).CSInterface) {
          const csInterface = new (window as any).CSInterface();
          csInterface.KVStorage.removeItem(key, () => {
            resolve();
          });
        } else {
          localStorage.removeItem(key);
          resolve();
        }
      } catch (error) {
        console.error('Error removing item from CEP storage:', error);
        resolve();
      }
    });
  }
};

// Default providers for SahAI V2
const createDefaultProviders = (): ProviderConfig[] => {
  return [
    {
      id: 'openai',
      name: 'OpenAI',
      type: 'openai',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.openai.com/v1',
      settings: { temperature: 0.7, maxTokens: 4096, timeout: 30000 },
    },
    {
      id: 'anthropic',
      name: 'Anthropic',
      type: 'anthropic',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.anthropic.com',
      settings: { temperature: 0.7, maxTokens: 4096, timeout: 30000 },
    },
    {
      id: 'gemini',
      name: 'Google Gemini',
      type: 'gemini',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://generativelanguage.googleapis.com/v1',
      settings: { temperature: 0.7, maxTokens: 4096, timeout: 30000 },
    },
    {
      id: 'deepseek',
      name: 'DeepSeek',
      type: 'deepseek',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.deepseek.com',
      settings: { temperature: 0.7, maxTokens: 4096, timeout: 30000 },
    },
    {
      id: 'groq',
      name: 'Groq',
      type: 'groq',
      isConfigured: false,
      isOnline: true,
      baseURL: 'https://api.groq.com/openai/v1',
      settings: { temperature: 0.7, maxTokens: 4096, timeout: 30000 },
    },
    {
      id: 'ollama',
      name: 'Ollama',
      type: 'ollama',
      isConfigured: true,
      isOnline: false,
      baseURL: 'http://localhost:11434',
      settings: { temperature: 0.7, maxTokens: 4096, timeout: 30000 },
    },
    {
      id: 'lmstudio',
      name: 'LM Studio',
      type: 'lmstudio',
      isConfigured: true,
      isOnline: false,
      baseURL: 'http://localhost:1234/v1',
      settings: { temperature: 0.7, maxTokens: 4096, timeout: 30000 },
    },
  ];
};

export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentModal: null,
      modalData: null,
      accessMethod: 'direct',
      isOpen: false,
      variant: 'slide-in',
      modalHistory: [],
      
      providers: [],
      currentProvider: null,
      availableModels: [],
      currentModel: null,
      modelsLoading: false,
      modelsError: null,
      
      theme: 'light',
      sidebarCollapsed: false,
      notifications: [],

      // Modal actions
      openModal: (modalType: ModalType, data?: ModalData, method: AccessMethod = 'direct', _variant: ModalVariant = 'slide-in') => {
        set({
          currentModal: modalType,
          modalData: data || null,
          accessMethod: method,
          isOpen: true,
          variant: 'slide-in',
          modalHistory: method === 'settings' && modalType !== 'settings' ? ['settings'] : [],
        });
      },

      openSubModal: (modalType: ModalType, data?: ModalData) => {
        const { currentModal, modalHistory } = get();
        
        set({
          currentModal: modalType,
          modalData: data || null,
          accessMethod: 'settings',
          isOpen: true,
          modalHistory: currentModal ? [...modalHistory, currentModal] : modalHistory,
        });
      },

      closeModal: () => {
        set({
          currentModal: null,
          modalData: null,
          isOpen: false,
          modalHistory: [],
        });
      },

      goBackToSettings: () => {
        const { modalHistory } = get();
        
          if (modalHistory.length > 0) {
            const previousModal = modalHistory[modalHistory.length - 1];
            const newHistory = modalHistory.slice(0, -1);
            
            set({
              currentModal: previousModal || null,
              modalData: null,
              modalHistory: newHistory,
            });
          } else {
            set({
              currentModal: 'settings',
              modalData: null,
              accessMethod: 'settings',
              modalHistory: [],
            });
          }
      },

      // Provider actions
      setCurrentProvider: async (providerId: string | null) => {
        const { providers } = get();
        const provider = providerId ? providers.find(p => p.id === providerId) : null;
        
        if (provider && providerId) {
          set({ currentProvider: provider, currentModel: null });
          // In a real implementation, this would load models
        } else {
          set({ currentProvider: null, currentModel: null, availableModels: [] });
        }
      },

      setCurrentModel: (modelId: string | null) => {
        const { availableModels } = get();
        const model = modelId ? availableModels.find(m => m.id === modelId) : null;
        set({ currentModel: model || null });
      },

      updateProviderConfig: (id: string, updates: Partial<ProviderConfig>) => {
        set((state) => ({
          providers: state.providers.map(provider =>
            provider.id === id ? { ...provider, ...updates } : provider
          ),
          currentProvider: state.currentProvider?.id === id 
            ? { ...state.currentProvider, ...updates }
            : state.currentProvider,
        }));
      },

      updateProviderApiKey: async (providerId: string, apiKey: string) => {
        try {
          await cepStorage.setItem(`sahai_api_key_${providerId}`, apiKey);
          
          const isLocalProvider = providerId === 'ollama' || providerId === 'lmstudio';
          get().updateProviderConfig(providerId, {
            apiKey,
            isConfigured: isLocalProvider ? true : !!apiKey
          });
        } catch (error) {
          console.error('Error updating API key:', error);
          throw error;
        }
      },

      // UI actions
      setTheme: (theme: 'light' | 'dark') => {
        set({ theme });
        document.documentElement.setAttribute('data-theme', theme);
      },

      toggleSidebar: () => {
        set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed }));
      },

      addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => {
        const id = Math.random().toString(36).substr(2, 9);
        set((state) => ({
          notifications: [...state.notifications, { ...notification, id, timestamp: new Date() }]
        }));
      },

      removeNotification: (id: string) => {
        set((state) => ({
          notifications: state.notifications.filter(n => n.id !== id)
        }));
      },

      clearNotifications: () => {
        set({ notifications: [] });
      },
    }),
    {
      name: 'sahai-ui-store',
      storage: cepStorage as any,
      partialize: (state) => ({
        providers: state.providers.length === 0 ? createDefaultProviders() : state.providers,
        currentProvider: state.currentProvider,
        currentModel: state.currentModel,
        theme: state.theme,
        sidebarCollapsed: state.sidebarCollapsed,
        availableModels: [],
        modelsLoading: false,
        modelsError: null,
        notifications: state.notifications.slice(-5), // Keep only last 5 notifications
      }),
    }
  )
);

// Initialize default providers if none exist
if (useUIStore.getState().providers.length === 0) {
  useUIStore.setState({ providers: createDefaultProviders() });
}

// Export convenience actions
export const uiActions = {
  openModal: (modalType: ModalType, data?: ModalData, method?: AccessMethod) =>
    useUIStore.getState().openModal(modalType, data, method),
  closeModal: () => useUIStore.getState().closeModal(),
  setCurrentProvider: (providerId: string | null) => useUIStore.getState().setCurrentProvider(providerId),
  setCurrentModel: (modelId: string | null) => useUIStore.getState().setCurrentModel(modelId),
  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) =>
    useUIStore.getState().addNotification(notification),
};
