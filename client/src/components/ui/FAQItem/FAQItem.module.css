@import url("../../../styles/adobe-tokens.css");

/* FAQ Item Container */
.faqItem {
  border: 1px solid var(--adobe-border);
  font-family: var(--adobe-font-family);
  transition: border-color 0.15s ease;
}

.faqItem:hover {
  border-color: var(--adobe-accent);
}

/* Question Button */
.question {
  width: 100%;
  padding: 10px 12px;
  background: var(--adobe-bg-secondary);
  border: none;
  color: var(--adobe-text-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  font-family: inherit;
  text-align: left;
  transition: background-color 0.15s ease;
}

.question:hover {
  background: var(--adobe-bg-hover);
}

.question:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: -2px;
}

.questionText {
  flex: 1;
  font-weight: 500;
}

.toggle {
  color: var(--adobe-text-secondary);
  display: flex;
  align-items: center;
  transition: transform 0.2s ease, color 0.15s ease;
}

.question:hover .toggle {
  color: var(--adobe-text-primary);
}

/* Answer Container with Animation */
.answerContainer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.open .answerContainer {
  max-height: 500px; /* Adjust based on content needs */
}

.answer {
  padding: 10px 12px;
  background: var(--adobe-bg-tertiary);
  font-size: 13px;
  line-height: 1.4;
  color: var(--adobe-text-primary);
  border-top: 1px solid var(--adobe-border);
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.2s ease 0.1s, transform 0.2s ease 0.1s;
}

.open .answer {
  opacity: 1;
  transform: translateY(0);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .answerContainer,
  .answer,
  .toggle {
    transition: none;
  }
  
  .open .answerContainer {
    max-height: none;
  }
}

/* Focus management for keyboard navigation */
.question:focus-visible {
  outline: 2px solid var(--adobe-accent);
  outline-offset: -2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .faqItem {
    border-width: 2px;
  }
  
  .question {
    font-weight: 600;
  }
}
