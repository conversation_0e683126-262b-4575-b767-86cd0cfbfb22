/**
 * FAQ Item Component with Animated Expand/Collapse
 * Provides consistent FAQ styling with smooth animations
 */

import React, { useState } from 'react';
import clsx from 'clsx';
import { ChevronRightIcon, ChevronDownIcon } from '../Icons/Icons';
import styles from './FAQItem.module.css';

export interface FAQItemProps {
  /** Question text */
  question: string;
  /** Answer text or JSX */
  answer: React.ReactNode;
  /** Whether the item is initially open */
  defaultOpen?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Custom question ID for accessibility */
  questionId?: string;
}

export const FAQItem: React.FC<FAQItemProps> = ({
  question,
  answer,
  defaultOpen = false,
  className,
  questionId,
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const toggle = () => setIsOpen(!isOpen);

  const itemClasses = clsx(
    styles.faqItem,
    {
      [styles.open]: isOpen,
    },
    className
  );

  return (
    <div className={itemClasses}>
      <button
        className={styles.question}
        onClick={toggle}
        aria-expanded={isOpen}
        aria-controls={questionId ? `${questionId}-answer` : undefined}
        id={questionId}
      >
        <span className={styles.questionText}>{question}</span>
        <span className={styles.toggle} aria-hidden="true">
          {isOpen ? (
            <ChevronDownIcon size={16} />
          ) : (
            <ChevronRightIcon size={16} />
          )}
        </span>
      </button>
      <div 
        className={styles.answerContainer}
        id={questionId ? `${questionId}-answer` : undefined}
        aria-labelledby={questionId}
      >
        <div className={styles.answer}>
          {answer}
        </div>
      </div>
    </div>
  );
};

export default FAQItem;
