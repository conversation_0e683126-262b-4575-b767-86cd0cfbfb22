/**
 * Unified Modal Component for V2
 * Slide-in modal system for consistent V2 user experience
 * Clinical precision implementation following audit recommendations
 */

import React, { useCallback, useTransition } from 'react';
import { useModalStore, ModalType } from '../../../stores/modalStore';
import {
  ChatHistoryModal,
  ProviderHealthModal,
  SettingsModal,
  AboutModal,
  HelpModal,
  AnalyticsModal
} from '../../modals';
import { SahAIModelConfiguration } from '../../SahAIModelConfiguration/SahAIModelConfiguration';
import './UnifiedModal.css';

interface ModalConfig {
  id: ModalType;
  label: string;
  component: React.ComponentType<{ onClose: () => void; onBack: () => void }>;
}

interface UnifiedModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// Modal categorization for different access methods
// const INDEPENDENT_MODALS: ModalType[] = [
//   'providerHealth',
//   'chatHistory',
//   'modelConfiguration'
// ];

const MODAL_CONFIGS: ModalConfig[] = [
  { id: 'providerHealth', label: 'Provider Health', component: ProviderHealthModal },
  { id: 'chatHistory', label: 'Chat History', component: ChatHistoryModal },
  { id: 'settings', label: 'Settings', component: SettingsModal },
  { id: 'about', label: 'About', component: AboutModal },
  { id: 'help', label: 'Help', component: HelpModal },
  { id: 'analytics', label: 'Analytics', component: AnalyticsModal },
];

/**
 * Unified Modal Component
 * Slide-in modal for V2 consistent user experience
 */
export const UnifiedModal: React.FC<UnifiedModalProps> = ({
  isOpen,
  onClose,
}) => {
  const { currentModal, accessMethod } = useModalStore();
  const [, startTransition] = useTransition();

  const handleBackToSettings = useCallback(() => {
    startTransition(() => {
      useModalStore.getState().goBackToSettings();
    });
  }, []);

  const handleClose = useCallback(() => {
    startTransition(() => {
      useModalStore.getState().closeModal();
    });
    onClose();
  }, [onClose]);

  if (!isOpen || !currentModal) {
    return null;
  }

  // Find the modal configuration
  const currentConfig = MODAL_CONFIGS.find(config => config.id === currentModal);

  // Determine if this modal should show settings navigation
  const showSettingsNav = accessMethod === 'settings' && currentModal !== 'settings';

  // Render slide-in modal (V2 standard) - Header removed since each modal has its own ModalHeader
  return (
    <div className={`slide-modal-overlay ${isOpen ? 'open' : ''}`}>
      {isOpen && (
        <div className={`slide-modal ${isOpen ? 'open' : ''}`}>
          {/* Handle modelConfiguration separately */}
          {currentModal === 'modelConfiguration' && (
            <SahAIModelConfiguration
              onClose={handleClose}
              isPopup={false}
            />
          )}

          {/* Render other modal content - each modal now has its own ModalHeader */}
          {currentConfig && currentModal !== 'modelConfiguration' && (
            <currentConfig.component
              onClose={handleClose}
              onBack={showSettingsNav ? handleBackToSettings : undefined}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default UnifiedModal;
