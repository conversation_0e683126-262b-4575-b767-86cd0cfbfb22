@import url("../../../styles/adobe-tokens.css");

/* Base skeleton styles */
.skeleton {
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: var(--adobe-radius);
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-family: var(--adobe-font-family);
}

/* Skeleton variants */
.kpi {
  text-align: center;
  min-height: 80px;
  justify-content: center;
}

.status {
  align-items: center;
}

/* Skeleton elements */
.label {
  height: 12px;
  background: var(--adobe-bg-tertiary);
  border-radius: 2px;
  width: 60%;
  animation: shimmer 1.5s ease-in-out infinite;
}

.kpi .label {
  align-self: center;
}

.value {
  height: 24px;
  background: var(--adobe-bg-tertiary);
  border-radius: 2px;
  width: 80%;
  margin-top: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
  animation-delay: 0.2s;
}

.kpi .value {
  align-self: center;
}

.progress {
  height: 4px;
  background: var(--adobe-bg-tertiary);
  border-radius: 2px;
  width: 100%;
  margin-top: 4px;
  animation: shimmer 1.5s ease-in-out infinite;
  animation-delay: 0.4s;
}

/* Shimmer animation */
@keyframes shimmer {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 400px) {
  .skeleton {
    padding: 8px;
  }
  
  .value {
    height: 20px;
  }
}
