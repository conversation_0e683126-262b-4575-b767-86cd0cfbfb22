/**
 * Skeleton Card Component for Loading States
 * Provides consistent loading placeholders for card components
 */

import React from 'react';
import clsx from 'clsx';
import styles from './SkeletonCard.module.css';

export interface SkeletonCardProps {
  /** Card variant to match */
  variant?: 'default' | 'kpi' | 'status';
  /** Additional CSS classes */
  className?: string;
  /** Show progress bar skeleton */
  showProgress?: boolean;
  /** Number of skeleton cards to render */
  count?: number;
}

export const SkeletonCard: React.FC<SkeletonCardProps> = ({
  variant = 'default',
  className,
  showProgress = false,
  count = 1,
}) => {
  const cardClasses = clsx(
    styles.skeleton,
    styles[variant],
    className
  );

  const renderSkeleton = () => (
    <div className={cardClasses}>
      <div className={styles.label}></div>
      <div className={styles.value}></div>
      {showProgress && <div className={styles.progress}></div>}
    </div>
  );

  if (count === 1) {
    return renderSkeleton();
  }

  return (
    <>
      {Array.from({ length: count }, (_, index) => (
        <React.Fragment key={index}>
          {renderSkeleton()}
        </React.Fragment>
      ))}
    </>
  );
};

export default SkeletonCard;
