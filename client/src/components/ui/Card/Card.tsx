/**
 * Reusable Card Component for CEP Modals
 * Provides consistent styling for KPI cards, info cards, and other content containers
 */

import React from 'react';
import clsx from 'clsx';
import styles from './Card.module.css';

export interface CardProps {
  /** Card content label */
  label?: string;
  /** Main value to display */
  value?: string | number;
  /** Optional unit for the value */
  unit?: string;
  /** Optional progress bar value (0-100) */
  progress?: number;
  /** Progress bar color */
  progressColor?: string;
  /** Additional CSS classes */
  className?: string;
  /** Card variant */
  variant?: 'default' | 'kpi' | 'status';
  /** Click handler */
  onClick?: () => void;
  /** Children for custom content */
  children?: React.ReactNode;
  /** Loading state */
  isLoading?: boolean;
}

export const Card: React.FC<CardProps> = ({
  label,
  value,
  unit,
  progress,
  progressColor,
  className,
  variant = 'default',
  onClick,
  children,
  isLoading = false,
}) => {
  const cardClasses = clsx(
    styles.card,
    styles[variant],
    {
      [styles.clickable]: onClick,
      [styles.loading]: isLoading,
    },
    className
  );

  if (children) {
    return (
      <div className={cardClasses} onClick={onClick}>
        {children}
      </div>
    );
  }

  return (
    <div className={cardClasses} onClick={onClick}>
      {label && <span className={styles.label}>{label}</span>}
      {value !== undefined && (
        <span className={styles.value}>
          {isLoading ? '...' : value}
          {unit && <span className={styles.unit}>{unit}</span>}
        </span>
      )}
      {progress !== undefined && (
        <div className={styles.progress}>
          <div 
            className={styles.fill} 
            style={{ 
              width: `${Math.min(100, Math.max(0, progress))}%`,
              backgroundColor: progressColor || 'var(--adobe-accent)'
            }} 
          />
        </div>
      )}
    </div>
  );
};

export default Card;
