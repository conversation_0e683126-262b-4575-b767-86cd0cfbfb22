@import url("../../../styles/adobe-tokens.css");

/* Base card styles */
.card {
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: var(--adobe-radius);
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-family: var(--adobe-font-family);
  transition: border-color 0.15s ease, background-color 0.15s ease;
}

.card:hover {
  border-color: var(--adobe-accent);
}

.clickable {
  cursor: pointer;
}

.clickable:hover {
  background: var(--adobe-bg-hover);
}

/* Card variants */
.kpi {
  text-align: center;
  min-height: 80px;
  justify-content: center;
}

.status {
  align-items: center;
}

/* Card content */
.label {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.value {
  font-size: 22px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  display: flex;
  align-items: baseline;
  justify-content: center;
}

.kpi .value {
  justify-content: center;
}

.status .value {
  justify-content: flex-start;
}

.unit {
  font-size: 12px;
  font-weight: 400;
  margin-left: 2px;
  color: var(--adobe-text-secondary);
}

/* Progress bar */
.progress {
  width: 100%;
  height: 4px;
  background: var(--adobe-bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 4px;
}

.fill {
  height: 100%;
  transition: width 0.4s ease;
  background: var(--adobe-accent);
}

/* Loading state */
.loading {
  opacity: 0.6;
}

.loading .value {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive adjustments */
@media (max-width: 400px) {
  .card {
    padding: 8px;
  }
  
  .value {
    font-size: 18px;
  }
}
