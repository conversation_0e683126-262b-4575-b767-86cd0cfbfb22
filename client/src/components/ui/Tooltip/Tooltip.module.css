@import url("../../../styles/adobe-tokens.css");

/* Container */
.container {
  position: relative;
  display: inline-block;
}

/* Tooltip */
.tooltip {
  position: absolute;
  z-index: 1000;
  background: var(--adobe-bg-tertiary);
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  padding: 6px 8px;
  font-family: var(--adobe-font-family);
  font-size: 12px;
  color: var(--adobe-text-primary);
  white-space: nowrap;
  max-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: scale(0.95);
  transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.2s ease;
  pointer-events: none;
}

.tooltip.visible {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

/* Content */
.content {
  position: relative;
  z-index: 1;
}

/* Arrow */
.arrow {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--adobe-bg-tertiary);
  border: 1px solid var(--adobe-border);
  transform: rotate(45deg);
}

/* Position variants */
.top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) scale(0.95);
  margin-bottom: 6px;
}

.top.visible {
  transform: translateX(-50%) scale(1);
}

.top .arrow {
  top: 100%;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  margin-top: -3px;
  border-top: none;
  border-left: none;
}

.bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%) scale(0.95);
  margin-top: 6px;
}

.bottom.visible {
  transform: translateX(-50%) scale(1);
}

.bottom .arrow {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  margin-bottom: -3px;
  border-bottom: none;
  border-right: none;
}

.left {
  right: 100%;
  top: 50%;
  transform: translateY(-50%) scale(0.95);
  margin-right: 6px;
}

.left.visible {
  transform: translateY(-50%) scale(1);
}

.left .arrow {
  left: 100%;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  margin-left: -3px;
  border-left: none;
  border-bottom: none;
}

.right {
  left: 100%;
  top: 50%;
  transform: translateY(-50%) scale(0.95);
  margin-left: 6px;
}

.right.visible {
  transform: translateY(-50%) scale(1);
}

.right .arrow {
  right: 100%;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  margin-right: -3px;
  border-right: none;
  border-top: none;
}

/* Responsive adjustments */
@media (max-width: 400px) {
  .tooltip {
    max-width: 150px;
    font-size: 11px;
    padding: 4px 6px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .tooltip {
    transition: opacity 0.1s ease, visibility 0.1s ease;
    transform: none;
  }

  .tooltip.visible {
    transform: none;
  }

  .top,
  .bottom,
  .left,
  .right {
    transform: none;
  }

  .top.visible,
  .bottom.visible,
  .left.visible,
  .right.visible {
    transform: none;
  }
}
