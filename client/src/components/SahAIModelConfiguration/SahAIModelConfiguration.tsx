/**
 * SahAI Model Configuration Component
 * Adapted from <PERSON>line's model selection logic for CEP environment
 * Replaces 'Cline' with 'SahAI' throughout
 */

import React, { useState, useEffect, useMemo } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { getProviderMetadata } from './utils/providerUtils';
import {
  ChevronDownIcon,
  CheckIcon,
  InfoIcon,
  SearchIcon,
  XIcon
} from '../ui';
import { ModalHeader } from '../modals/ModalHeader';
import './SahAIModelConfiguration.css';

interface SahAIModelConfigurationProps {
  onClose: () => void;
  isPopup?: boolean;
}

export const SahAIModelConfiguration: React.FC<SahAIModelConfigurationProps> = ({
  onClose,
  isPopup = false,
}) => {
  const {
    providers,
    currentProvider,
    availableModels,
    currentModel,
    modelsLoading,
    modelsError,
    setCurrentProvider,
    setCurrentModel,
    loadModelsForProvider,
    updateProviderApiKey,
  } = useSettingsStore();

  // Local state for UI
  const [selectedProviderId, setSelectedProviderId] = useState(currentProvider?.id || '');
  const [selectedModelId, setSelectedModelId] = useState(currentModel?.id || '');
  const [apiKey, setApiKey] = useState('');
  const [showApiKeyInput, setShowApiKeyInput] = useState(false);
  const [modelSearchQuery, setModelSearchQuery] = useState('');
  const [isProviderDropdownOpen, setIsProviderDropdownOpen] = useState(false);
  const [isModelDropdownOpen, setIsModelDropdownOpen] = useState(false);

  // Initialize state from current settings
  useEffect(() => {
    if (currentProvider) {
      setSelectedProviderId(currentProvider.id);
      setShowApiKeyInput(!currentProvider.isConfigured);
    }
    if (currentModel) {
      setSelectedModelId(currentModel.id);
    }
  }, [currentProvider, currentModel]);

  // Auto-save functionality (adapted from Cline's pattern)
  useEffect(() => {
    const autoSave = async () => {
      if (selectedProviderId && selectedProviderId !== currentProvider?.id) {
        await setCurrentProvider(selectedProviderId);
      }
      if (selectedModelId && selectedModelId !== currentModel?.id) {
        setCurrentModel(selectedModelId);
      }
    };

    const timeoutId = setTimeout(autoSave, 500); // Debounce auto-save
    return () => clearTimeout(timeoutId);
  }, [selectedProviderId, selectedModelId, currentProvider, currentModel, setCurrentProvider, setCurrentModel]);

  // Initialize with current provider and model
  useEffect(() => {
    if (currentProvider && !selectedProviderId) {
      setSelectedProviderId(currentProvider.id);
      setShowApiKeyInput(!currentProvider.isConfigured);
    }
    if (currentModel && !selectedModelId) {
      setSelectedModelId(currentModel.id);
    }
  }, [currentProvider, currentModel, selectedProviderId, selectedModelId]);

  // Filter models based on search query
  const filteredModels = useMemo(() => {
    if (!modelSearchQuery.trim()) {
      return availableModels;
    }
    
    const query = modelSearchQuery.toLowerCase();
    return availableModels.filter(model =>
      model.name.toLowerCase().includes(query) ||
      model.id.toLowerCase().includes(query) ||
      (model.description && model.description.toLowerCase().includes(query))
    );
  }, [availableModels, modelSearchQuery]);

  // Handle provider selection
  const handleProviderSelect = async (providerId: string) => {
    const provider = providers.find(p => p.id === providerId);
    if (!provider) return;

    setSelectedProviderId(providerId);
    setIsProviderDropdownOpen(false);
    setSelectedModelId(''); // Reset model selection
    setModelSearchQuery(''); // Reset search

    // Auto-apply: Immediately set the current provider (Cline-style)
    if (provider.isConfigured) {
      await setCurrentProvider(providerId);
    }

    // Show API key input only if provider requires API key and is not configured
    const providerMetadata = getProviderMetadata(providerId);
    if (!provider.isConfigured && providerMetadata?.requiresApiKey) {
      setShowApiKeyInput(true);
    } else {
      setShowApiKeyInput(false);
      // For local providers (Ollama, LM Studio), auto-configure them
      if (!provider.isConfigured && !providerMetadata?.requiresApiKey) {
        await setCurrentProvider(providerId);
      }
      // Load models for configured provider
      await loadModelsForProvider(providerId);
    }
  };

  // Handle model selection
  const handleModelSelect = (modelId: string) => {
    setSelectedModelId(modelId);
    setIsModelDropdownOpen(false);
    setModelSearchQuery('');

    // Auto-apply: Immediately set the current model (Cline-style)
    setCurrentModel(modelId);
  };

  // Handle API key submission
  const handleApiKeySubmit = async () => {
    if (!selectedProviderId || !apiKey.trim()) return;

    try {
      await updateProviderApiKey(selectedProviderId, apiKey.trim());
      setShowApiKeyInput(false);
      setApiKey('');

      // Auto-apply: Set as current provider after API key is configured
      await setCurrentProvider(selectedProviderId);

      // Load models after API key is set
      await loadModelsForProvider(selectedProviderId);
    } catch (error) {
      console.error('Failed to update API key:', error);
      // TODO: Show error message to user
    }
  };

  // Get provider display info
  const getProviderInfo = (providerId: string) => {
    const provider = providers.find(p => p.id === providerId);
    if (!provider) return null;

    const infoMap: Record<string, { description: string; website: string }> = {
      openai: {
        description: 'OpenAI\'s GPT models including GPT-4 and GPT-3.5',
        website: 'https://platform.openai.com/api-keys',
      },
      anthropic: {
        description: 'Anthropic\'s Claude models for advanced reasoning',
        website: 'https://console.anthropic.com/',
      },
      google: {
        description: 'Google\'s Gemini models for multimodal AI',
        website: 'https://makersuite.google.com/app/apikey',
      },
    };

    return infoMap[providerId] || { description: provider.name, website: '' };
  };

  const selectedProviderInfo = selectedProviderId ? getProviderInfo(selectedProviderId) : null;
  const selectedProvider = providers.find(p => p.id === selectedProviderId);
  const selectedModelInfo = availableModels.find(m => m.id === selectedModelId);

  return (
    <div className="modal-content">
      {!isPopup && <ModalHeader title="Model Configuration" onClose={onClose} />}
      <div className={`sahai-model-configuration ${isPopup ? 'popup' : ''}`}>
        {/* Provider Selection */}
        <div className="config-section">
          <label className="config-label">AI Provider</label>
          <div className="dropdown-container">
            <button
              className={`dropdown-trigger ${isProviderDropdownOpen ? 'open' : ''}`}
              onClick={() => setIsProviderDropdownOpen(!isProviderDropdownOpen)}
              aria-expanded={isProviderDropdownOpen}
            >
              <span className="dropdown-text">
                {selectedProvider ? selectedProvider.name : 'Select Provider'}
              </span>
              <ChevronDownIcon size={14} className="dropdown-icon" />
            </button>
            
            {isProviderDropdownOpen && (
              <div className="dropdown-menu">
                {providers.map(provider => (
                  <button
                    key={provider.id}
                    className={`dropdown-item ${selectedProviderId === provider.id ? 'selected' : ''}`}
                    onClick={() => handleProviderSelect(provider.id)}
                  >
                    <span className="provider-name">{provider.name}</span>
                    {provider.isConfigured && (
                      <CheckIcon size={14} className="configured-icon" />
                    )}
                  </button>
                ))}
              </div>
            )}
          </div>
          
          {selectedProviderInfo && (
            <p className="provider-description">
              {selectedProviderInfo.description}
            </p>
          )}
        </div>

        {/* API Key Input */}
        {showApiKeyInput && selectedProvider && (
          <div className="config-section">
            <label className="config-label">API Key</label>
            <div className="api-key-input-container">
              <input
                type="password"
                className="api-key-input"
                placeholder={`Enter your ${selectedProvider.name} API key`}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleApiKeySubmit()}
              />
              <button
                className="api-key-submit"
                onClick={handleApiKeySubmit}
                disabled={!apiKey.trim()}
              >
                Save
              </button>
            </div>
            
            {selectedProviderInfo?.website && (
              <p className="api-key-help">
                Get your API key from{' '}
                <a 
                  href={selectedProviderInfo.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="api-key-link"
                >
                  {selectedProvider.name}
                </a>
              </p>
            )}
          </div>
        )}

        {/* Model Selection */}
        {selectedProvider?.isConfigured && (
          <div className="config-section">
            <label className="config-label">Model</label>
            
            {modelsLoading ? (
              <div className="loading-state">
                <InfoIcon size={16} className="loading-icon" />
                <span>Loading models...</span>
              </div>
            ) : modelsError ? (
              <div className="error-state">
                <XIcon size={16} className="error-icon" />
                <span>Error loading models: {modelsError}</span>
              </div>
            ) : (
              <>
                <div className="model-search-container">
                  <SearchIcon size={14} className="search-icon" />
                  <input
                    type="text"
                    className="model-search-input"
                    placeholder="Search models..."
                    value={modelSearchQuery}
                    onChange={(e) => setModelSearchQuery(e.target.value)}
                    onFocus={() => setIsModelDropdownOpen(true)}
                  />
                </div>
                
                <div className="dropdown-container">
                  <button
                    className={`dropdown-trigger ${isModelDropdownOpen ? 'open' : ''}`}
                    onClick={() => setIsModelDropdownOpen(!isModelDropdownOpen)}
                    aria-expanded={isModelDropdownOpen}
                  >
                    <span className="dropdown-text">
                      {selectedModelInfo ? selectedModelInfo.name : 'Select Model'}
                    </span>
                    <ChevronDownIcon size={14} className="dropdown-icon" />
                  </button>
                  
                  {isModelDropdownOpen && (
                    <div className="dropdown-menu model-dropdown">
                      {filteredModels.length > 0 ? (
                        filteredModels.map(model => (
                          <button
                            key={model.id}
                            className={`dropdown-item ${selectedModelId === model.id ? 'selected' : ''}`}
                            onClick={() => handleModelSelect(model.id)}
                          >
                            <div className="model-item">
                              <span className="model-name">{model.name}</span>
                              {model.description && (
                                <span className="model-description">{model.description}</span>
                              )}
                            </div>
                          </button>
                        ))
                      ) : (
                        <div className="no-models">
                          <InfoIcon size={16} />
                          <span>No models found</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        )}

        {/* Model Information */}
        {selectedModelInfo && (
          <div className="config-section">
            <label className="config-label">Model Information</label>
            <div className="model-info">
              <div className="model-info-item">
                <span className="info-label">Name:</span>
                <span className="info-value">{selectedModelInfo.name}</span>
              </div>
              
              {selectedModelInfo.description && (
                <div className="model-info-item">
                  <span className="info-label">Description:</span>
                  <span className="info-value">{selectedModelInfo.description}</span>
                </div>
              )}
              
              {selectedModelInfo.contextLength && (
                <div className="model-info-item">
                  <span className="info-label">Context Length:</span>
                  <span className="info-value">{selectedModelInfo.contextLength.toLocaleString()} tokens</span>
                </div>
              )}
              
              {selectedModelInfo.capabilities && selectedModelInfo.capabilities.length > 0 && (
                <div className="model-info-item">
                  <span className="info-label">Capabilities:</span>
                  <div className="capabilities-list">
                    {selectedModelInfo.capabilities.map((capability, index) => (
                      <span key={index} className="capability-tag">
                        {capability}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SahAIModelConfiguration;
