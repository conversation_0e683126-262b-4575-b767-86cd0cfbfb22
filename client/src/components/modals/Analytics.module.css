@import url("../../styles/adobe-tokens.css");
@import url("../../styles/animations.css");

/* ---------- wrapper ---------- */
.wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: var(--adobe-font-family);
  background: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
}

/* ---------- header row ---------- */
.headerRow {
  display: flex;
  gap: 8px;
  align-items: center;
}
.timeSelect,
.refresh {
  height: 24px;
  padding: 0 8px;
  border: 1px solid var(--adobe-border);
  border-radius: 0;
  background: var(--adobe-bg-secondary);
  color: var(--adobe-text-primary);
  font-size: 12px;
  cursor: pointer;
}
.refresh:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.spin {
  animation: spin 1s linear infinite;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ---------- body ---------- */
.body {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

/* ---------- KPI ---------- */
.kpiGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
  gap: 12px;
}

.kpiCard {
  composes: hover-lift from '../../styles/animations.css';
}

/* ---------- latency card ---------- */
.latencyCard {
  composes: hover-border from '../../styles/animations.css';
}

.latencyContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  font-size: 12px;
  text-transform: uppercase;
  color: var(--adobe-text-secondary);
}

.big {
  font-size: 20px;
  font-weight: 600;
  color: var(--adobe-text-primary);
}

/* ---------- tips ---------- */
.tips h4 {
  font-size: 14px;
  margin-bottom: 8px;
  color: var(--adobe-text-primary);
}
.tips ul {
  list-style: none;
  padding-left: 0;
  font-size: 13px;
  color: var(--adobe-text-secondary);
}
.tips li {
  margin-bottom: 4px;
}