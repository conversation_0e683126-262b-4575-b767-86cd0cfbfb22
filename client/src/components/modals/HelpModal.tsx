/**
 * Adobe CEP Help Modal
 * One-file, no overrides, ready to drop in
 */

import React, { useState } from 'react';
import { ModalHeader } from './ModalHeader';
import styles from './Help.module.css';

interface Props {
  onClose: () => void;
  onBack?: () => void;
}

export const HelpModal: React.FC<Props> = ({ onClose, onBack }) => {
  const [open, setOpen] = useState<string | null>(null);

  const faq = [
    {
      q: 'How do I set up API keys?',
      a: 'Settings → Advanced Config → enter keys (encrypted locally).',
    },
    {
      q: 'Which Adobe apps are supported?',
      a: 'After Effects, Premiere Pro, Photoshop & Illustrator 2025+.',
    },
    {
      q: 'How to compare models?',
      a: 'Use Model Comparison in settings for side-by-side metrics.',
    },
    {
      q: 'Multi-model chat?',
      a: 'Enable Multi-Model Chat to query several AIs at once.',
    },
    {
      q: 'Troubleshooting connection issues?',
      a: 'Check keys, network, provider status – logs in Analytics.',
    },
    {
      q: 'Is my data secure?',
      a: 'Yes – keys encrypted, conversations ephemeral.',
    },
  ];

  const toggle = (id: string) => setOpen(open === id ? null : id);

  return (
    <div className={styles.wrapper}>
      <ModalHeader
        title="Help & Support"
        onClose={onClose}
        onBack={onBack}
        showBackButton={true}
      />

      <main className={styles.body}>
        <h2 className={styles.title}>Quick Start</h2>
        <ol className={styles.steps}>
          <li>Configure provider key.</li>
          <li>Select model.</li>
          <li>Start chatting.</li>
          <li>Use Analytics & compare models.</li>
        </ol>

        <h2 className={styles.title}>FAQ</h2>
        <div className={styles.faqList}>
          {faq.map((item, idx) => (
            <div key={idx} className={styles.qa}>
              <button
                className={styles.question}
                onClick={() => toggle(String(idx))}
              >
                {item.q}
                <span className={styles.toggle}>{open === String(idx) ? '−' : '+'}</span>
              </button>
              {open === String(idx) && (
                <div className={styles.answer}>{item.a}</div>
              )}
            </div>
          ))}
        </div>

        <h2 className={styles.title}>Need More Help?</h2>
        <div className={styles.links}>
          <button>📖 Docs</button>
          <button>💬 Community</button>
          <button>🐛 Report Bug</button>
          <button>✉️ Support</button>
        </div>

        <h2 className={styles.title}>Pro Tips</h2>
        <ul className={styles.tips}>
          <li><strong>Ctrl+Enter</strong> → send</li>
          <li>Hover for tooltips</li>
          <li>Try different models</li>
          <li>Save key chats</li>
        </ul>
      </main>
    </div>
  );
};