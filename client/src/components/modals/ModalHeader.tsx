/**
 * Reusable Modal Header Component
 * Adobe CEP-style header with 40px height, proper typography, and monochrome SVG icons
 * Drop-in component for consistent modal headers across all modals
 */

import React from 'react';
import styles from './ModalHeader.module.css';

interface ModalHeaderProps {
  title: string;
  onClose: () => void;
  onBack?: () => void;
  showBackButton?: boolean;
  children?: React.ReactNode; // For additional header content like search bars
}

export const ModalHeader: React.FC<ModalHeaderProps> = ({
  title,
  onClose,
  onBack,
  showBackButton = false,
  children
}) => {
  return (
    <header className={styles.header}>
      <div className={styles.left}>
        {(showBackButton || onBack) && onBack && (
          <button
            onClick={onBack}
            className={styles.iconBtn}
            aria-label="Back"
            type="button"
          >
            <LeftArrow />
          </button>
        )}
        <h1 className={styles.title}>{title}</h1>
      </div>

      {children && (
        <div className="modal-header-center">
          {children}
        </div>
      )}

      <button
        onClick={onClose}
        className={styles.iconBtn}
        aria-label="Close"
        type="button"
      >
        <CloseIcon />
      </button>
    </header>
  );
};

/* ---------- monochrome SVG icons ---------- */
const LeftArrow = () => (
  <svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M15 8H3m0 0l4-4m-4 4l4 4"/>
  </svg>
);

const CloseIcon = () => (
  <svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M6 6l4 4m-4 0l4-4"/>
  </svg>
);

// Export default for easier imports
export default ModalHeader;
