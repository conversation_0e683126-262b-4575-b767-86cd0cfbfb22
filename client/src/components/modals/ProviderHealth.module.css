@import url("../../styles/adobe-tokens.css");

.wrapper{
  display:flex;
  flex-direction:column;
  height:100%;
}

.refresh{
  background:none;
  border:none;
  color:var(--adobe-text-secondary);
  padding:4px;
  border-radius:2px;
  cursor:pointer;
  transition:background .15s;
}
.refresh:hover{background:var(--adobe-bg-hover);}
.spin{animation:spin 1s linear infinite;}
@keyframes spin{to{transform:rotate(360deg);}}

.body{padding:16px;overflow-y:auto;}

.modelName{
  font-size:15px;
  font-weight:600;
  color:var(--adobe-text-primary);
  margin-bottom:16px;
  text-align:center;
}

.grid{
  display:grid;
  grid-template-columns:repeat(2,1fr);
  gap:12px;
}

.card{
  background:var(--adobe-bg-secondary);
  border:1px solid var(--adobe-border);
  border-radius:0;
  padding:12px;
  display:flex;
  flex-direction:column;
  gap:4px;
}

.label{
  font-size:11px;
  text-transform:uppercase;
  letter-spacing:.5px;
  color:var(--adobe-text-secondary);
}

.value{
  font-size:22px;
  font-weight:600;
}

.progress{
  width:100%;
  height:4px;
  background:var(--adobe-bg-tertiary);
  border-radius:2px;
  overflow:hidden;
}
.fill{height:100%;transition:width .4s ease;}

.log{margin-top:16px;}
.logTitle{
  font-size:12px;
  color:var(--adobe-text-secondary);
}
.logLine{
  font-family:Consolas,Monaco,monospace;
  font-size:12px;
  margin-top:4px;
  color:var(--adobe-text-secondary);
}