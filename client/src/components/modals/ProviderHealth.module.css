@import url("../../styles/adobe-tokens.css");

.wrapper{
  display:flex;
  flex-direction:column;
  height:100%;
}

.refresh{
  background:none;
  border:none;
  color:var(--adobe-text-secondary);
  padding:4px;
  border-radius:2px;
  cursor:pointer;
  transition:background .15s;
}
.refresh:hover{background:var(--adobe-bg-hover);}
.spin{animation:spin 1s linear infinite;}
@keyframes spin{to{transform:rotate(360deg);}}

.body{padding:16px;overflow-y:auto;}

.modelName{
  font-size:15px;
  font-weight:600;
  color:var(--adobe-text-primary);
  margin-bottom:16px;
  text-align:center;
}

/* Status Legend */
.legend {
  margin-bottom: 16px;
  padding: 8px 12px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 0;
}

.legendTitle {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-right: 12px;
}

.legendItems {
  display: inline-flex;
  gap: 16px;
}

.legendItem {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--adobe-text-primary);
}

.dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.good { background: #00B341; }
.warning { background: #FFB900; }
.critical { background: #FF453A; }

.grid{
  display:grid;
  grid-template-columns:repeat(2,1fr);
  gap:12px;
}

.healthCard {
  transition: border-color 0.15s ease, transform 0.15s ease;
}

.healthCard:hover {
  border-color: var(--adobe-accent);
  transform: translateY(-1px);
}

.label{
  font-size:11px;
  text-transform:uppercase;
  letter-spacing:.5px;
  color:var(--adobe-text-secondary);
}

.value{
  font-size:22px;
  font-weight:600;
}

.progress{
  width:100%;
  height:4px;
  background:var(--adobe-bg-tertiary);
  border-radius:2px;
  overflow:hidden;
}
.fill{height:100%;transition:width .4s ease;}

.log{margin-top:16px;}
.logTitle{
  font-size:12px;
  color:var(--adobe-text-secondary);
}
.logLine{
  font-family:Consolas,Monaco,monospace;
  font-size:12px;
  margin-top:4px;
  color:var(--adobe-text-secondary);
}