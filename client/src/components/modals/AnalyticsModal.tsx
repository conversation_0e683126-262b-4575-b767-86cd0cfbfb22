/**
 * Analytics Modal – Adobe CEP Edition
 * Dark, rectangular, monochrome, 40 px header, no radius, Source Sans Pro
 */

import React, { useState, useEffect } from 'react';
import { useChatStore } from '../../stores/chatStore';
import { useSettingsStore } from '../../stores/settingsStore';
import { ModalHeader } from './ModalHeader';
import { RefreshIcon } from '../ui';
import styles from './Analytics.module.css';

interface Props {
  onClose: () => void;
  onBack?: () => void;
}

export const AnalyticsModal: React.FC<Props> = ({ onClose, onBack }) => {
  const { sessions } = useChatStore();
  const { currentProvider } = useSettingsStore();
  const [isLoading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');

  /* ---------- mock data (replace with real API) ---------- */
  const stats = {
    messages: sessions.reduce((s, c) => s + c.messages.length, 0),
    sessions: sessions.length,
    tokens: sessions.length * 1234,
    cost: sessions.length * 0.0004,
    avgLatency: 1.4 + Math.random() * 0.8,
  };
  /* -------------------------------------------------------- */

  const refresh = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 600);
  };

  return (
    <div className={styles.wrapper}>
      <ModalHeader
        title="Analytics"
        onClose={onClose}
        onBack={onBack}
        showBackButton={true}
      >
        {/* header controls – inline, no box */}
        <div className={styles.headerRow}>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className={styles.timeSelect}
          >
            <option value="7d">7 days</option>
            <option value="30d">30 days</option>
            <option value="90d">90 days</option>
            <option value="all">All time</option>
          </select>

          <button onClick={refresh} disabled={isLoading} className={styles.refresh}>
            <RefreshIcon size={14} className={isLoading ? styles.spin : ''} />
          </button>
        </div>
      </ModalHeader>

      <div className={styles.body}>
        {/* KPI cards */}
        <div className={styles.kpiGrid}>
          <Card label="Messages" value={stats.messages} unit="" />
          <Card label="Sessions" value={stats.sessions} unit="" />
          <Card label="Tokens Used" value={stats.tokens} unit="" />
          <Card label="Est. Cost" value={stats.cost.toFixed(4)} unit="$" />
        </div>

        {/* latency & tips */}
        <div className={styles.latencyBox}>
          <span className={styles.label}>Average Latency</span>
          <span className={styles.big}>{stats.avgLatency.toFixed(1)} s</span>
        </div>

        <div className={styles.tips}>
          <h4>Performance Tips</h4>
          <ul>
            <li>Use concise prompts to cut cost & latency.</li>
            <li>Pick faster models for simple tasks.</li>
            <li>Monitor usage daily to avoid surprises.</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

/* ---------- small helper ---------- */
const Card: React.FC<{ label: string; value: number | string; unit: string }> = ({ label, value, unit }) => (
  <div className={styles.card}>
    <span className={styles.cardLabel}>{label}</span>
    <span className={styles.cardValue}>
      {value}
      {unit && <span className={styles.unit}>{unit}</span>}
    </span>
  </div>
);