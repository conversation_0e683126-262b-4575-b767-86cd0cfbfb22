/**
 * Standardized Modal Content Styles for V2
 * Adobe CEP-compatible design with consistent spacing and typography
 * Unified modal system for all modal types
 * Modal appears below topbar, covering message composer and input area
 */

/* import tokens at the very top */
@import url("../../styles/adobe-tokens.css");

/* ===== MODAL SYSTEM BASE ===== */

/* ===== modal-content – only wrapper ===== */
.modal-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.modal-body {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

/* All modal header styles removed - now handled by ModalHeader component */


/* Legacy modal-actions for backward compatibility */
.modal-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* ===== ADOBE CEP BUTTONS ===== */
/* Adobe-style accent button */
.adobe-btn {
  height: 24px;
  padding: 0 12px;
  border: none;
  border-radius: 2px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.15s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--adobe-font-family);
}

.adobe-btn.primary {
  background: var(--adobe-accent);
  color: #fff;
}

.adobe-btn.primary:hover {
  background: var(--adobe-accent-hover);
}

.adobe-btn.secondary {
  background: var(--adobe-bg-tertiary);
  color: var(--adobe-text-primary);
  border: 1px solid var(--adobe-border);
}

.adobe-btn.secondary:hover {
  background: var(--adobe-bg-hover);
}

.adobe-btn:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 1px;
}

.adobe-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal body - standardized scrollable content area */
.modal-body {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px; /* Add padding for Adobe CEP style */
}

/* ===== ADOBE CEP INPUTS ===== */
/* Adobe-style input field */
.adobe-input {
  height: 24px;
  padding: 0 8px;
  border: 1px solid var(--adobe-border);
  border-radius: 2px;
  background: var(--adobe-bg-secondary);
  color: var(--adobe-text-primary);
  font-size: 12px;
  font-family: var(--adobe-font-family);
  transition: border-color 0.15s ease;
}

.adobe-input:focus {
  outline: none;
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.adobe-input::placeholder {
  color: var(--adobe-text-secondary);
}

/* ===== HEADER SEARCH COMPONENTS ===== */
/* Search input in modal header */
.header-search-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  max-width: 400px;
}

.header-search {
  flex: 1;
  min-width: 200px;
}

.header-sort {
  min-width: 120px;
}

.search-icon {
  color: var(--adobe-text-secondary);
  flex-shrink: 0;
}

.session-info {
  padding: 8px 0;
  border-bottom: 1px solid var(--adobe-border);
  margin-bottom: 16px;
}

.session-count {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  font-weight: 500;
}

/* Header controls for action buttons */
.header-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-controls .adobe-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}

.has-changes {
  background: var(--adobe-success) !important;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== ADOBE CEP CARDS AND CONTAINERS ===== */
/* Remove all border-radius from cards and containers */
.card,
.info-card,
.feature-card,
.provider-card,
.model-card,
.analytics-card,
.comparison-card,
.health-card {
  border-radius: 0 !important; /* Adobe CEP panels are rectangular */
  border: 1px solid var(--adobe-border);
  background: var(--adobe-bg-secondary);
}

/* Modal footer - standardized across all modals */
.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid var(--adobe-border);
  margin-top: 8px;
  gap: 16px;
  flex-shrink: 0;
}

/* ===== STANDARDIZED FORM ELEMENTS ===== */

.modal-content input,
.modal-content textarea,
.modal-content select {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  background: var(--adobe-bg-tertiary, #3c3c3c);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 6px;
  color: var(--adobe-text-primary, #ffffff);
  font-size: 14px;
  font-family: var(--adobe-font-family);
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  box-sizing: border-box;
}

.modal-content textarea {
  height: auto;
  min-height: 80px;
  resize: vertical;
}

.modal-content input:focus,
.modal-content textarea:focus,
.modal-content select:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.modal-content input:disabled,
.modal-content textarea:disabled,
.modal-content select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--adobe-bg-secondary, #2d2d2d);
}

/* ===== STANDARDIZED BUTTONS ===== */

.modal-content button {
  height: 40px;
  padding: 8px 16px;
  background: var(--adobe-accent);
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  font-family: var(--adobe-font-family);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  box-sizing: border-box;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.modal-content button:hover:not(:disabled) {
  background: var(--adobe-accent-hover);
  transform: translateY(-1px);
}

.modal-content button:focus {
  box-shadow: 0 0 0 2px var(--adobe-accent);
}

.modal-content button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Button variants */
.modal-content button.secondary {
  background: var(--adobe-bg-tertiary, #3c3c3c);
  color: var(--adobe-text-primary, #ffffff);
  border: 1px solid var(--adobe-border, #555555);
}

.modal-content button.secondary:hover:not(:disabled) {
  background: var(--adobe-bg-tertiary);
  border-color: var(--adobe-accent);
}

.modal-content button.danger {
  background: var(--adobe-bg-danger, #ff4444);
}

.modal-content button.danger:hover:not(:disabled) {
  background: var(--adobe-error);
}

.modal-content button.icon-only {
  width: 40px;
  height: 40px;
  padding: 0;
  background: var(--adobe-bg-tertiary, #3c3c3c);
  color: var(--adobe-text-secondary, #cccccc);
  border: 1px solid var(--adobe-border, #555555);
}

.modal-content button.icon-only:hover:not(:disabled) {
  background: var(--adobe-bg-tertiary);
  border-color: var(--adobe-accent);
  color: var(--adobe-text-primary);
}

/* ===== STANDARDIZED LAYOUT COMPONENTS ===== */

/* Toolbar - standardized across all modals */
.modal-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  border-bottom: 1px solid var(--adobe-border, #555555);
  margin-bottom: 16px;
  flex-shrink: 0;
}

.modal-toolbar-primary {
  flex: 1;
  min-width: 0;
}

.modal-toolbar-secondary {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* Card - standardized container for content blocks */
.modal-card {
  background: var(--adobe-bg-tertiary, #3c3c3c);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.modal-card:hover {
  background: var(--adobe-bg-tertiary);
  border-color: var(--adobe-accent);
}

.modal-card.active {
  border-color: var(--adobe-accent);
  background: var(--adobe-bg-secondary);
}

.modal-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 12px;
}

.modal-card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary, #ffffff);
  margin: 0;
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.modal-card-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.modal-card-body {
  color: var(--adobe-text-secondary, #cccccc);
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 12px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.modal-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--adobe-text-tertiary, #999999);
  gap: 8px;
}

/* List - standardized scrollable list container */
.modal-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  min-height: 0;
  overflow-y: auto;
}

.modal-list-item {
  cursor: pointer;
}

/* Grid - standardized grid layout */
.modal-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

/* Stats - standardized statistics display */
.modal-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  padding: 16px;
  background: var(--adobe-bg-secondary, #2d2d2d);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 8px;
}

.modal-stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
}

.modal-stat-label {
  font-size: 12px;
  color: var(--adobe-text-secondary, #cccccc);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.modal-stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--adobe-text-primary, #ffffff);
}

/* Empty state - standardized across all modals */
.modal-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  color: var(--adobe-text-secondary, #cccccc);
  flex: 1;
}

.modal-empty-icon {
  opacity: 0.5;
  margin-bottom: 16px;
  font-size: 48px;
}

.modal-empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--adobe-text-primary, #ffffff);
  margin: 0 0 8px 0;
}

.modal-empty-description {
  font-size: 14px;
  margin: 0;
  max-width: 400px;
}

/* ===== STANDARDIZED SCROLLBARS ===== */

.modal-content::-webkit-scrollbar,
.modal-body::-webkit-scrollbar,
.modal-list::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track,
.modal-body::-webkit-scrollbar-track,
.modal-list::-webkit-scrollbar-track {
  background: var(--adobe-bg-primary, #1e1e1e);
}

.modal-content::-webkit-scrollbar-thumb,
.modal-body::-webkit-scrollbar-thumb,
.modal-list::-webkit-scrollbar-thumb {
  background: var(--adobe-border, #555555);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover,
.modal-body::-webkit-scrollbar-thumb:hover,
.modal-list::-webkit-scrollbar-thumb:hover {
  background: var(--adobe-text-secondary, #cccccc);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Medium screens */
@media (max-width: 768px) {
  .modal-content {
    padding: 16px;
    gap: 12px;
  }
  
  .modal-toolbar,
  .searchSection {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .modal-toolbar-primary,
  .modal-toolbar-secondary,
  .searchInput,
  .searchButton,
  .sortSelect {
    width: 100%;
  }

  .modal-toolbar-secondary {
    justify-content: center;
  }

  .searchButton {
    height: 40px;
  }

  .sortSelect {
    min-width: unset;
    max-width: unset;
  }
  
  .modal-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-stats {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}

/* Small screens */
@media (max-width: 480px) {
  .modal-content {
    padding: 12px;
    gap: 8px;
  }
  
  .modal-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .modal-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .modal-card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .modal-card-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .modal-stats {
    grid-template-columns: 1fr;
  }
}

/* ===== ANIMATIONS ===== */

.modal-fade-in {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== ABOUT MODAL STYLES ===== */

/* About Sections */
.about-section {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

/* Logo Section */
.logo-section {
  text-align: center;
  padding: 15px 0;
}

.app-icon {
  font-size: 48px;
  margin-bottom: 10px;
  display: block;
}

.app-name {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--adobe-text-primary);
}

.version {
  font-size: 14px;
  color: var(--adobe-text-secondary);
}

/* Info Section */
.info-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.section-header {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin-bottom: 5px;
}

.section-description {
  font-size: 14px;
  color: var(--adobe-text-secondary);
  line-height: 1.5;
}

/* Features List */
.features-list {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 10px;
  padding-left: 0;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.feature-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.feature-item div {
  font-size: 14px;
  color: var(--adobe-text-primary);
}

.feature-item strong {
  color: var(--adobe-accent);
}

/* Links Section */
.links-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.link-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.link-button {
  padding: 8px 12px;
  background-color: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-accent);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  font-family: var(--adobe-font-family);
}

.link-button:hover {
  background-color: var(--adobe-bg-tertiary);
  border-color: var(--adobe-accent-hover);
}

/* Copyright Section */
.copyright-section {
  margin-top: 20px;
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid var(--adobe-border);
}

.copyright {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  margin-bottom: 5px;
}

.disclaimer {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  font-style: italic;
}

/* ===== HELP MODAL STYLES ===== */

/* Help Sections */
.help-section {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.subsection-header {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid var(--adobe-border);
}

/* Quick Start Guide */
.quick-start {
  margin-bottom: 20px;
}

.steps-list {
  padding-left: 20px;
  margin-bottom: 20px;
}

.steps-list li {
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--adobe-text-primary);
}

.steps-list strong {
  color: var(--adobe-accent);
}

/* FAQ Section */
.faq-section {
  margin-bottom: 20px;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.faq-item {
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  overflow: hidden;
}

.faq-question {
  width: 100%;
  padding: 12px 15px;
  background-color: var(--adobe-bg-secondary);
  border: none;
  color: var(--adobe-text-primary);
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  transition: background-color 0.2s ease;
  font-family: var(--adobe-font-family);
}

.faq-question:hover {
  background-color: var(--adobe-bg-tertiary);
}

.faq-toggle {
  font-size: 18px;
  font-weight: bold;
  margin-left: 10px;
  color: var(--adobe-accent);
}

.faq-answer {
  padding: 15px;
  background-color: var(--adobe-bg-primary);
  font-size: 13px;
  color: var(--adobe-text-secondary);
  line-height: 1.5;
  border-top: 1px solid var(--adobe-border);
}

/* Support Section */
.support-section {
  margin-bottom: 20px;
}

.support-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 20px;
}

.support-button {
  padding: 10px 12px;
  background-color: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-accent);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: var(--adobe-font-family);
}

.support-button:hover {
  background-color: var(--adobe-bg-tertiary);
  border-color: var(--adobe-accent-hover);
}

/* Tips Section */
.tips-section {
  margin-bottom: 20px;
}

.tips-list {
  list-style: none;
  padding-left: 0;
}

.tip-item {
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.5;
  padding-left: 20px;
  position: relative;
  color: var(--adobe-text-primary);
}

.tip-item::before {
  content: "•";
  color: var(--adobe-accent);
  font-weight: bold;
  position: absolute;
  left: 5px;
}

.tip-item strong {
  color: var(--adobe-accent);
}

/* ===== SETTINGS MODAL STYLES ===== */

/* Settings Sections */
.settings-section {
  margin-bottom: 30px;
}

.settings-section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--adobe-border);
}

/* Settings Links */
.settings-link {
  width: 100%;
  padding: 12px 15px;
  background-color: transparent;
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  color: var(--adobe-text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
  text-align: left;
  font-family: var(--adobe-font-family);
}

.settings-link:hover {
  background-color: var(--adobe-bg-tertiary);
  border-color: var(--adobe-accent);
}

.settings-icon {
  font-size: 20px;
  flex-shrink: 0;
  width: 24px;
  text-align: center;
}

.settings-link-text {
  flex: 1;
  min-width: 0;
}

.settings-link-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin-bottom: 2px;
}

.settings-link-description {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  line-height: 1.4;
}

/* ===== CHAT HISTORY MODAL STYLES ===== */

/* Session count in header */
.session-count {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  background-color: var(--adobe-bg-secondary);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid var(--adobe-border);
}

/* Search section */
.search-section {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  align-items: center;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--adobe-text-secondary);
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  background-color: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
  font-size: 14px;
  font-family: var(--adobe-font-family);
}

.search-input:focus {
  outline: none;
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.sort-select {
  padding: 8px 12px;
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  background-color: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
  font-size: 14px;
  font-family: var(--adobe-font-family);
  min-width: 140px;
}

.sort-select:focus {
  outline: none;
  border-color: var(--adobe-accent);
}

/* Session list */
.session-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
  max-height: 400px;
  overflow-y: auto;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--adobe-text-secondary);
}

.empty-icon {
  color: var(--adobe-text-secondary);
  margin-bottom: 16px;
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin-bottom: 8px;
}

.empty-description {
  font-size: 14px;
  color: var(--adobe-text-secondary);
}

/* Session items */
.session-item {
  padding: 16px;
  border: 1px solid var(--adobe-border);
  border-radius: 6px;
  background-color: var(--adobe-bg-primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.session-item:hover {
  border-color: var(--adobe-accent);
  background-color: var(--adobe-bg-tertiary);
}

.current-session {
  border-color: var(--adobe-accent);
  background-color: var(--adobe-bg-secondary);
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.session-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin: 0;
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
}

.session-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.session-date {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--adobe-text-secondary);
}

.delete-button {
  background: none;
  border: none;
  color: var(--adobe-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  transition: all 0.2s ease;
}

.delete-button:hover {
  color: var(--adobe-error);
  background-color: var(--adobe-bg-tertiary);
}

.session-preview {
  font-size: 13px;
  color: var(--adobe-text-secondary);
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.session-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.message-count {
  color: var(--adobe-text-secondary);
}

.session-provider {
  background-color: var(--adobe-bg-secondary);
  color: var(--adobe-text-secondary);
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  border: 1px solid var(--adobe-border);
}

/* History summary */
.history-summary {
  padding-top: 16px;
  border-top: 1px solid var(--adobe-border);
  text-align: center;
}

.summary-text {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  margin: 0;
}

/* ===== PROVIDER HEALTH MODAL STYLES ===== */

/* Refresh button */
.refresh-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: var(--adobe-accent);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: var(--adobe-font-family);
}

.refresh-button:hover:not(:disabled) {
  background-color: var(--adobe-accent-hover);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

/* Health overview */
.health-overview {
  margin-bottom: 24px;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-item {
  background-color: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 6px;
  padding: 16px;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: var(--adobe-text-secondary);
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: var(--adobe-text-primary);
}

/* Providers health list */
.providers-health-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.provider-health-item {
  border: 1px solid var(--adobe-border);
  border-radius: 6px;
  background-color: var(--adobe-bg-primary);
  overflow: hidden;
}

.provider-info {
  padding: 16px;
}

.provider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.provider-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin: 0;
}

.provider-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-text {
  font-size: 12px;
  font-weight: 600;
  text-transform: capitalize;
}

.provider-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}

.detail-label {
  color: var(--adobe-text-secondary);
  font-weight: 500;
}

.detail-value {
  color: var(--adobe-text-primary);
  text-align: right;
  max-width: 60%;
  word-wrap: break-word;
}

/* ===== MODAL-SPECIFIC IMPLEMENTATIONS ===== */

/* Chat History Modal */
.chatHistorySection {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.searchSection {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  border-bottom: 1px solid var(--adobe-border, #555555);
  margin-bottom: 16px;
  flex-shrink: 0;
}

.searchInput {
  flex: 1;
  min-width: 0;
  height: 40px;
  padding: 8px 12px;
  background: var(--adobe-bg-tertiary, #3c3c3c);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 6px;
  color: var(--adobe-text-primary, #ffffff);
  font-size: 14px;
  box-sizing: border-box;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.searchInput:focus {
  border-color: var(--adobe-color-primary, #0a84ff);
  box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.2);
}

.searchButton {
  width: 40px;
  height: 40px;
  padding: 0;
  background: var(--adobe-bg-tertiary, #3c3c3c);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 6px;
  color: var(--adobe-text-secondary, #cccccc);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-sizing: border-box;
  outline: none;
}

.searchButton:hover {
  background: var(--adobe-bg-hover, #4a4a4a);
  border-color: var(--adobe-color-primary, #0a84ff);
  color: var(--adobe-text-primary, #ffffff);
}

.searchButton:focus {
  border-color: var(--adobe-color-primary, #0a84ff);
  box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.2);
}

.sortSelect {
  min-width: 120px;
  max-width: 160px;
  height: 40px;
  padding: 8px 12px;
  background: var(--adobe-bg-tertiary, #3c3c3c);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 6px;
  color: var(--adobe-text-primary, #ffffff);
  font-size: 14px;
  cursor: pointer;
  flex-shrink: 0;
  box-sizing: border-box;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.sortSelect:focus {
  border-color: var(--adobe-color-primary, #0a84ff);
  box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.2);
}

.sessionList {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.sessionItem {
  background: var(--adobe-bg-tertiary, #3c3c3c);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.sessionItem:hover {
  background: var(--adobe-bg-hover, #4a4a4a);
  border-color: var(--adobe-color-primary, #0a84ff);
}

.currentSession {
  border-color: var(--adobe-color-primary, #0a84ff);
  background: rgba(10, 132, 255, 0.1);
}

.sessionHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 12px;
}

.sessionTitle {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary, #ffffff);
  margin: 0;
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.sessionActions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.sessionDate {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--adobe-text-secondary, #cccccc);
  white-space: nowrap;
}

.deleteButton {
  background: none;
  border: none;
  color: var(--adobe-text-secondary, #cccccc);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  min-width: 24px;
  min-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
}

.deleteButton:hover {
  background: var(--adobe-bg-danger, #ff4444);
  color: white;
}

.sessionPreview {
  font-size: 14px;
  color: var(--adobe-text-secondary, #cccccc);
  line-height: 1.4;
  margin-bottom: 8px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.sessionMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--adobe-text-tertiary, #999999);
  gap: 8px;
}

.messageCount {
  font-weight: 500;
  white-space: nowrap;
}

.sessionProvider {
  background: var(--adobe-bg-secondary, #2d2d2d);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  white-space: nowrap;
  flex-shrink: 0;
}

.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: var(--adobe-text-secondary, #cccccc);
}

.emptyIcon {
  opacity: 0.5;
  margin-bottom: 16px;
}

.emptyState h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: var(--adobe-text-primary, #ffffff);
}

.emptyState p {
  margin: 0;
  font-size: 14px;
}

.historySummary {
  padding: 12px 0;
  border-top: 1px solid var(--adobe-border, #555555);
  text-align: center;
  font-size: 12px;
  color: var(--adobe-text-secondary, #cccccc);
  flex-shrink: 0;
}

.historySummary p {
  margin: 0;
}

/* Provider Health Modal */
.health-dashboard {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.dashboard-header {
  /* Uses modal-header classes */
}

.health-overview {
  /* Uses modal-stats classes */
}

.providers-health-list {
  /* Uses modal-list classes */
}

.provider-health-item {
  /* Uses modal-card classes */
}

/* Settings Modal */
.settings-modal {
  display: flex;
  height: 100%;
  gap: 20px;
}

.settings-sidebar {
  width: 280px;
  background: var(--adobe-bg-secondary, #2d2d2d);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 8px;
  padding: 16px;
  flex-shrink: 0;
}

.settings-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.settings-nav-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: var(--adobe-text-primary, #ffffff);
  cursor: pointer;
  text-align: left;
  transition: background 0.2s ease;
  width: 100%;
  box-sizing: border-box;
  font-size: 14px;
  font-family: var(--adobe-font-family);
  height: auto;
}

.settings-nav-item:hover {
  background: var(--adobe-bg-tertiary, #3c3c3c);
}

.settings-nav-item.active {
  background: rgba(10, 132, 255, 0.1);
  color: var(--adobe-color-primary, #0a84ff);
}

.nav-item-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.nav-item-title {
  font-size: 14px;
  font-weight: 500;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.nav-item-description {
  font-size: 12px;
  color: var(--adobe-text-secondary, #cccccc);
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.settings-content {
  flex: 1;
  background: var(--adobe-bg-secondary, #2d2d2d);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 8px;
  padding: 24px;
  overflow-y: auto;
  min-width: 0;
}

/* Settings responsive */
@media (max-width: 768px) {
  .settings-modal {
    flex-direction: column;
    gap: 16px;
  }
  
  .settings-sidebar {
    width: 100%;
    order: 2;
  }
  
  .settings-content {
    order: 1;
  }
}

/* ===== UTILITY CLASSES ===== */

.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.flex-1 { flex: 1; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-grow-0 { flex-grow: 0; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-0 { margin-bottom: 0; }
.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }

.gap-4 { gap: 4px; }
.gap-8 { gap: 8px; }
.gap-12 { gap: 12px; }
.gap-16 { gap: 16px; }
.gap-24 { gap: 24px; }

.p-8 { padding: 8px; }
.p-12 { padding: 12px; }
.p-16 { padding: 16px; }
.p-24 { padding: 24px; }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.whitespace-nowrap { white-space: nowrap; }
.break-words { word-wrap: break-word; overflow-wrap: break-word; }

/* ===== NEW MODAL STYLES ===== */

/* Advanced Config Modal */
.config-section {
  margin-bottom: 24px;
}

.tab-navigation {
  display: flex;
  gap: 4px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--adobe-border);
}

.tab-button {
  padding: 8px 16px;
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--adobe-text-secondary);
  cursor: pointer;
  font-size: 14px;
  font-family: var(--adobe-font-family);
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: var(--adobe-text-primary);
}

.tab-button.active {
  color: var(--adobe-accent);
  border-bottom-color: var(--adobe-accent);
}

.tab-content {
  min-height: 300px;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--adobe-text-primary);
  margin-bottom: 6px;
}

.setting-input {
  width: 100%;
  padding: 8px 12px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  color: var(--adobe-text-primary);
  font-size: 14px;
  font-family: var(--adobe-font-family);
}

.setting-input:focus {
  outline: none;
  border-color: var(--adobe-accent);
}

.setting-slider {
  width: 100%;
  height: 4px;
  background: var(--adobe-bg-secondary);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.setting-select {
  width: 100%;
  padding: 8px 12px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  color: var(--adobe-text-primary);
  font-size: 14px;
  font-family: var(--adobe-font-family);
  cursor: pointer;
}

.setting-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--adobe-text-primary);
  cursor: pointer;
}

.setting-checkbox input[type="checkbox"] {
  margin: 0;
}

.save-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: var(--adobe-accent);
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  font-family: var(--adobe-font-family);
  cursor: pointer;
  transition: all 0.2s ease;
}

.save-button:hover:not(:disabled) {
  background: var(--adobe-accent-hover);
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.save-button.has-changes {
  background: #4caf50;
}

.reset-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  color: var(--adobe-text-primary);
  font-size: 14px;
  font-family: var(--adobe-font-family);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 8px;
}

.reset-button:hover {
  background: var(--adobe-bg-tertiary);
  border-color: var(--adobe-accent);
}

.changes-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid #ffc107;
  border-radius: 4px;
  color: #ffc107;
  font-size: 13px;
  margin-top: 16px;
}

/* Model Comparison Modal */
.model-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.model-card {
  padding: 16px;
  background: var(--adobe-bg-secondary);
  border: 2px solid var(--adobe-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.model-card:hover {
  border-color: var(--adobe-accent);
}

.model-card.selected {
  border-color: var(--adobe-accent);
  background: rgba(10, 132, 255, 0.1);
}

.model-header {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 12px;
}

.model-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin: 0;
}

.provider-name {
  font-size: 12px;
  color: var(--adobe-text-secondary);
}

.quality-rating {
  display: flex;
  gap: 2px;
  margin-top: 4px;
}

.star-filled {
  color: #ffc107;
}

.star-empty {
  color: var(--adobe-text-secondary);
}

.model-metrics {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.model-capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.capability-tag {
  padding: 2px 6px;
  background: var(--adobe-bg-tertiary);
  border-radius: 12px;
  font-size: 11px;
  color: var(--adobe-text-secondary);
}

.capability-tag-small {
  padding: 1px 4px;
  background: var(--adobe-bg-tertiary);
  border-radius: 8px;
  font-size: 10px;
  color: var(--adobe-text-secondary);
  margin-right: 4px;
  margin-bottom: 2px;
}

.capability-more {
  padding: 2px 6px;
  background: var(--adobe-accent);
  border-radius: 12px;
  font-size: 11px;
  color: white;
}

.comparison-table {
  overflow-x: auto;
  margin-bottom: 24px;
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: var(--adobe-bg-secondary);
  border-radius: 8px;
  overflow: hidden;
}

.table th,
.table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid var(--adobe-border);
}

.table th {
  background: var(--adobe-bg-tertiary);
  font-weight: 600;
  color: var(--adobe-text-primary);
}

.table td {
  color: var(--adobe-text-secondary);
}

.capabilities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  margin-bottom: 4px;
}

.feature-list li:before {
  content: "•";
  color: var(--adobe-accent);
  margin-right: 6px;
}

.rating-display {
  display: flex;
  gap: 2px;
}

.no-selection {
  text-align: center;
  padding: 40px;
  color: var(--adobe-text-secondary);
}

.no-selection-icon {
  color: var(--adobe-text-secondary);
  margin-bottom: 16px;
}

.recommendations {
  margin-top: 24px;
}

.recommendation-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.recommendation-card {
  padding: 12px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 6px;
  text-align: center;
}

.recommendation-card h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--adobe-text-primary);
}

.recommendation-card p {
  margin: 0;
  font-size: 12px;
  color: var(--adobe-text-secondary);
}

.sort-select {
  padding: 6px 12px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  font-family: var(--adobe-font-family);
}

/* Multi-Model Modal */
.model-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.model-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: var(--adobe-bg-secondary);
  border: 2px solid var(--adobe-border);
  border-radius: 20px;
  color: var(--adobe-text-primary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.model-chip:hover:not(.disabled) {
  border-color: var(--adobe-accent);
}

.model-chip.selected {
  border-color: var(--adobe-accent);
  background: rgba(10, 132, 255, 0.1);
}

.model-chip.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.model-chip-name {
  font-weight: 500;
}

.model-chip-provider {
  font-size: 10px;
  color: var(--adobe-text-secondary);
}

.chip-icon {
  color: var(--adobe-accent);
}

.prompt-input-section {
  margin-bottom: 24px;
}

.prompt-input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.prompt-textarea {
  flex: 1;
  padding: 12px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 6px;
  color: var(--adobe-text-primary);
  font-size: 14px;
  font-family: var(--adobe-font-family);
  resize: vertical;
  min-height: 80px;
}

.prompt-textarea:focus {
  outline: none;
  border-color: var(--adobe-accent);
}

.send-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--adobe-accent);
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  font-family: var(--adobe-font-family);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.send-button:hover:not(:disabled) {
  background: var(--adobe-accent-hover);
}

.send-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.comparison-section {
  margin-bottom: 24px;
}

.comparison-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.comparison-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.prompt-preview {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  font-style: italic;
}

.comparison-time {
  font-size: 11px;
  color: var(--adobe-text-secondary);
}

.responses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.response-card {
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 8px;
  padding: 16px;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.response-model-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin: 0;
}

.response-provider {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  margin-left: 4px;
}

.response-time {
  font-size: 12px;
  color: var(--adobe-text-secondary);
}

.response-content {
  min-height: 100px;
}

.response-text {
  font-size: 13px;
  color: var(--adobe-text-primary);
  line-height: 1.5;
  white-space: pre-wrap;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--adobe-text-secondary);
  font-size: 13px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--adobe-border);
  border-top: 2px solid var(--adobe-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff6b6b;
  font-size: 13px;
}

.error-icon {
  font-size: 16px;
}

.session-history {
  margin-top: 24px;
}

.session-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.session-item {
  padding: 12px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.session-item:hover {
  border-color: var(--adobe-accent);
}

.session-item.active {
  border-color: var(--adobe-accent);
  background: rgba(10, 132, 255, 0.1);
}

.session-prompt {
  font-size: 13px;
  color: var(--adobe-text-primary);
  margin-bottom: 6px;
}

.session-meta {
  display: flex;
  justify-content: space-between;
  font-size: 11px;
  color: var(--adobe-text-secondary);
}

.no-sessions {
  text-align: center;
  color: var(--adobe-text-secondary);
  font-style: italic;
  padding: 20px;
}

.back-to-setup {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  color: var(--adobe-text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 16px;
  text-decoration: none;
}

.back-to-setup:hover {
  background: var(--adobe-bg-tertiary);
  border-color: var(--adobe-accent);
}

.selected-count {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  background: var(--adobe-bg-secondary);
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid var(--adobe-border);
}