@import url("../../styles/adobe-tokens.css");

/* ---------- layout ---------- */
.wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
  font-family: var(--adobe-font-family);
}

.body {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.title {
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--adobe-text-primary);
}

/* ---------- quick start ---------- */
.steps {
  padding-left: 18px;
  font-size: 14px;
  line-height: 1.5;
}
.steps li {
  margin-bottom: 4px;
}

/* ---------- FAQ ---------- */
.faqList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.qa {
  border: 1px solid var(--adobe-border);
}
.question {
  width: 100%;
  padding: 10px 12px;
  background: var(--adobe-bg-secondary);
  border: none;
  color: var(--adobe-text-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
}
.toggle {
  font-size: 16px;
  color: var(--adobe-text-secondary);
}
.answer {
  padding: 10px 12px;
  background: var(--adobe-bg-tertiary);
  font-size: 13px;
  line-height: 1.4;
  border-top: 1px solid var(--adobe-border);
}

/* ---------- links ---------- */
.links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}
.links button {
  padding: 8px 12px;
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  color: var(--adobe-text-primary);
  font-size: 13px;
  cursor: pointer;
  text-align: left;
  transition: border-color 0.15s;
}
.links button:hover {
  border-color: var(--adobe-accent);
}

/* ---------- tips ---------- */
.tips {
  padding-left: 16px;
  font-size: 13px;
  line-height: 1.5;
}
.tips strong {
  color: var(--adobe-accent);
}