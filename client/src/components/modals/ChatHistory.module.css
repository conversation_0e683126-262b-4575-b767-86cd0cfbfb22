@import url("../../styles/adobe-tokens.css");
@import url("../../styles/accessibility.css");

.wrapper{display:flex;flex-direction:column;height:100%;}
.body{flex:1;padding:16px;overflow-y:auto;}

/* Use shared accessibility utilities */
.srOnly {
  composes: sr-only from '../../styles/accessibility.css';
}

/* search row – icon on right, no box */
.searchRow{
  display:flex;
  align-items:center;
  gap:8px;
  flex:1;
}
.searchInput{
  flex:1;
  height:24px;
  padding:0 8px;
  background:var(--adobe-bg-tertiary);
  border:1px solid var(--adobe-border);
  border-radius:0;
  color:var(--adobe-text-primary);
  font-size:13px;
}
.searchInput:focus{outline:none;border-color:var(--adobe-accent);}
.iconBtn{
  background:none;border:none;color:var(--adobe-text-secondary);
  padding:4px;border-radius:2px;cursor:pointer;
}
.iconBtn:hover{color:var(--adobe-text-primary);}
.sortSelect{
  height:24px;
  padding:0 8px;
  background:var(--adobe-bg-tertiary);
  border:1px solid var(--adobe-border);
  color:var(--adobe-text-primary);
  font-size:13px;
}

.count{
  font-size:12px;
  color:var(--adobe-text-secondary);
  margin-bottom:12px;
}

.sessionList{display:flex;flex-direction:column;gap:8px;}
.empty{text-align:center;color:var(--adobe-text-secondary);padding:40px 0;}

.item{
  composes: accessible-list-item from '../../styles/accessibility.css';
  background:var(--adobe-bg-secondary);
  border:1px solid var(--adobe-border);
  padding:12px;
  cursor:pointer;
  transition: border-color 0.15s ease, background-color 0.15s ease;
  position: relative;
}
.item:hover{border-color:var(--adobe-accent);}
.item:focus{outline:2px solid var(--adobe-accent);outline-offset:-2px;}
.item.active{border-color:var(--adobe-accent);background:var(--adobe-bg-hover);}
.item.active::before{
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--adobe-accent);
}

.header{display:flex;align-items:center;gap:8px;margin-bottom:6px;}
.header h4{flex:1;font-size:14px;font-weight:600;color:var(--adobe-text-primary);}
.date{font-size:11px;color:var(--adobe-text-secondary);display:flex;align-items:center;gap:4px;}
.trash{background:none;border:none;color:var(--adobe-text-secondary);cursor:pointer;}
.trash:hover{color:var(--adobe-error);}
.preview{font-size:13px;color:var(--adobe-text-secondary);margin-bottom:4px;}
.meta{display:flex;justify-content:space-between;font-size:11px;color:var(--adobe-text-secondary);}
.provider{background:var(--adobe-bg-tertiary);padding:2px 6px;border-radius:2px;font-size:10px;}