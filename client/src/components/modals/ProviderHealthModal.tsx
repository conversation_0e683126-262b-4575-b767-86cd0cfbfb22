// src/components/modals/ProviderHealthModal.tsx
import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { ModalHeader } from './ModalHeader';
import { RefreshIcon } from '../ui';
import styles from './ProviderHealth.module.css';

interface Props {
  onClose: () => void;
  onBack?: () => void;
}

interface HealthData {
  cpu: string;
  memory: string;
  latency: string;
  status: 'Online' | 'Degraded' | 'Offline';
  statusClass: 'good' | 'warning' | 'critical';
}

export const ProviderHealthModal: React.FC<Props> = ({ onClose, onBack }) => {
  const { currentProvider } = useSettingsStore();
  const [data, setData] = useState<HealthData | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  const fetchHealth = async () => {
    setIsChecking(true);
    await new Promise(r => setTimeout(r, 600)); // simulate API
    setData({
      cpu: (Math.random() * 80 + 10).toFixed(1),
      memory: (Math.random() * 7 + 1).toFixed(2),
      latency: (Math.random() * 150 + 30).toFixed(0),
      status: Math.random() < 0.8 ? 'Online' : Math.random() < 0.95 ? 'Degraded' : 'Offline',
      statusClass: Math.random() < 0.8 ? 'good' : Math.random() < 0.95 ? 'warning' : 'critical',
    });
    setIsChecking(false);
  };

  useEffect(() => {
    fetchHealth();
  }, [currentProvider]);

  const colorMap = { good: '#00B341', warning: '#FFB900', critical: '#FF453A' };

  return (
    <div className={styles.wrapper}>
      <ModalHeader title="Provider Health" onClose={onClose} onBack={onBack} showBackButton={true}>
        <button className={styles.refresh} onClick={fetchHealth} disabled={isChecking}>
          <RefreshIcon size={16} className={isChecking ? styles.spin : ''} />
        </button>
      </ModalHeader>

      <div className={styles.body}>
        {/* Provider name */}
        <div className={styles.modelName}>{currentProvider?.name || 'No provider selected'}</div>

        {/* 4-widget grid */}
        <div className={styles.grid}>
          {[
            { label: 'CPU Usage', value: `${data?.cpu ?? '--'}%`, bar: data ? +data.cpu : 0, color: colorMap[data?.statusClass ?? 'good'] },
            { label: 'Memory', value: `${data?.memory ?? '--'} GB`, bar: data ? (+data.memory / 8) * 100 : 0, color: colorMap[data?.statusClass ?? 'good'] },
            { label: 'Latency', value: `${data?.latency ?? '--'} ms`, bar: data ? (+data.latency / 200) * 100 : 0, color: colorMap[data?.statusClass ?? 'good'] },
            { label: 'Status', value: data?.status ?? '--', color: colorMap[data?.statusClass ?? 'good'] },
          ].map(({ label, value, bar, color }) => (
            <div key={label} className={styles.card}>
              <span className={styles.label}>{label}</span>
              <span className={styles.value} style={{ color }}>{value}</span>

              {/* progress bar if needed */}
              {bar !== undefined && (
                <div className={styles.progress}>
                  <div className={styles.fill} style={{ width: `${bar}%`, backgroundColor: color }} />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* mini log */}
        <div className={styles.log}>
          <span className={styles.logTitle}>System Log</span>
          <p className={styles.logLine}>
            {data ? `[${new Date().toLocaleTimeString()}] Health data fetched.` : 'Fetching…'}
          </p>
        </div>
      </div>
    </div>
  );
};