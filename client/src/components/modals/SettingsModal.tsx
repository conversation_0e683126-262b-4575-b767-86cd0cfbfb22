/**
 * Settings Modal for V2
 * Enhanced settings interface with improved design and navigation
 * Inspired by Settings_modal_demo.html
 */

import React from 'react';
import { useModalStore } from '../../stores/modalStore';
import { ModalHeader } from './ModalHeader';
import styles from './ModalContent.module.css';

interface SettingsModalProps {
  onClose: () => void;
  onBack: () => void;
}

interface SettingLink {
  id: string;
  title: string;
  description: string;
  icon: string;
  modalType: string;
}

const SETTINGS_LINKS: SettingLink[] = [
  {
    id: 'analytics',
    title: 'Analytics',
    description: 'View usage statistics and performance metrics',
    icon: '📊',
    modalType: 'analytics'
  },
  {
    id: 'help',
    title: 'Help & Support',
    description: 'Get help and find answers to common questions',
    icon: '❓',
    modalType: 'help'
  },
  {
    id: 'about',
    title: 'About',
    description: 'Learn more about SahAI Extension',
    icon: 'ℹ️',
    modalType: 'about'
  }
];

export const SettingsModal: React.FC<SettingsModalProps> = ({
  onClose,
  onBack
}) => {
  const { openSubModal } = useModalStore();

  const handleSettingClick = (modalType: string) => {
    console.log(`Opening: ${modalType}`);
    // Open the corresponding sub-modal
    openSubModal(modalType as any);
  };

  return (
    <div className={styles['modal-content']}>
      <ModalHeader
        title="Settings"
        onClose={onClose}
      />

      <div className={styles['modal-body']}>
        <div className={styles['settings-section']}>
          {SETTINGS_LINKS.map((link) => (
            <button
              key={link.id}
              className={styles['settings-link']}
              onClick={() => handleSettingClick(link.modalType)}
            >
              <span className={styles['settings-icon']}>{link.icon}</span>
              <div className={styles['settings-link-text']}>
                <div className={styles['settings-link-title']}>{link.title}</div>
                <div className={styles['settings-link-description']}>{link.description}</div>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;
