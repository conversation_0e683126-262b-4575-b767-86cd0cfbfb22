/**
 * Chat History Modal – Adobe CEP Style
 * Search icon moved to right → acts as click-to-search button
 * Search input ↔ button ↔ sort select – no extra boxes
 */

import React, { useState, useMemo } from 'react';
import { useChatStore } from '../../stores/chatStore';
import { useModalStore } from '../../stores/modalStore';
import { ModalHeader } from './ModalHeader';
import { SearchIcon, TrashIcon, ClockIcon } from '../ui';
import styles from './ChatHistory.module.css';

interface Props {
  onSelectSession?: (id: string) => void;
  onClose: () => void;
  onBack?: () => void;
}

export const ChatHistoryModal: React.FC<Props> = ({
  onSelectSession,
  onClose,
  onBack,
}) => {
  const { sessions, currentSession, deleteSession, loadSession } = useChatStore();
  const { closeModal } = useModalStore();
  const [query, setQuery] = useState('');
  const [sort, setSort] = useState<'recent' | 'oldest' | 'alphabetical'>('recent');

  const list = useMemo(() => {
    let res = sessions.filter(s =>
      s.title.toLowerCase().includes(query.toLowerCase()) ||
      s.messages.some(m =>
        typeof m.content === 'string' &&
        m.content.toLowerCase().includes(query.toLowerCase())
      )
    );
    if (sort === 'recent')
      return res.sort((a, b) => b.updatedAt - a.updatedAt);
    if (sort === 'oldest')
      return res.sort((a, b) => a.updatedAt - b.updatedAt);
    return res.sort((a, b) => a.title.localeCompare(b.title));
  }, [sessions, query, sort]);

  const select = (id: string) => {
    loadSession(id);
    onSelectSession?.(id);
    closeModal();
  };

  const remove = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('Delete this session?')) deleteSession(id);
  };

  const fmtDate = (t: number) => {
    const d = new Date(t);
    const diff = Math.ceil((Date.now() - t) / 86400000);
    if (diff === 1) return 'Today';
    if (diff === 2) return 'Yesterday';
    if (diff <= 7) return `${diff} days ago`;
    return d.toLocaleDateString();
  };

  return (
    <div className={styles.wrapper}>
      <ModalHeader title="Chat History" onClose={onClose} onBack={onBack}>
        {/* Search bar – icon on right */}
        <div className={styles.searchRow}>
          <input
            type="text"
            placeholder="Search chat history…"
            value={query}
            onChange={e => setQuery(e.target.value)}
            className={styles.searchInput}
          />
          <button className={styles.iconBtn} aria-label="Search">
            <SearchIcon size={16} />
          </button>

          <select value={sort} onChange={e => setSort(e.target.value as any)} className={styles.sortSelect}>
            <option value="recent">Most Recent</option>
            <option value="oldest">Oldest First</option>
            <option value="alphabetical">Alphabetical</option>
          </select>
        </div>
      </ModalHeader>

      <div className={styles.body}>
        {/* count */}
        <div className={styles.count}>{list.length} session{list.length !== 1 && 's'}</div>

        {/* list */}
        <div className={styles.sessionList}>
          {list.length === 0 ? (
            <div className={styles.empty}>
              <p>No sessions found</p>
            </div>
          ) : (
            list.map(s => (
              <div
                key={s.id}
                className={`${styles.item} ${s.id === currentSession?.id ? styles.active : ''}`}
                onClick={() => select(s.id)}
              >
                <div className={styles.header}>
                  <h4>{s.title}</h4>
                  <span className={styles.date}>
                    <ClockIcon size={12} /> {fmtDate(s.updatedAt)}
                  </span>
                  <button onClick={e => remove(s.id, e)} className={styles.trash}>
                    <TrashIcon size={14} />
                  </button>
                </div>
                <p className={styles.preview}>
                  {s.messages[s.messages.length - 1]?.content?.toString().slice(0, 100) || 'No preview'}…
                </p>
                <div className={styles.meta}>
                  <span>{s.messages.length} messages</span>
                  {s.metadata?.provider && <span className={styles.provider}>{s.metadata.provider}</span>}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};