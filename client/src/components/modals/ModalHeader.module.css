.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 12px;
  background: var(--adobe-bg-secondary);
  border-bottom: 1px solid var(--adobe-border);
  flex-shrink: 0;
}

.left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title {
  font-size: 14px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin: 0;
}

.iconBtn {
  background: none;
  border: none;
  color: var(--adobe-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  transition: 0.15s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconBtn:hover {
  background: var(--adobe-bg-hover);
  color: var(--adobe-text-primary);
}
