/**
 * Adobe CEP About Modal – drop-in replacement
 * No blue close button, full CEP styling
 */

import React from 'react';
import clsx from 'clsx';
import { ModalHeader } from './ModalHeader';
import { StarIcon, InfoIcon } from '../ui';
import styles from './About.module.css';

interface Props {
  onClose: () => void;
  onBack?: () => void;
}

export const AboutModal: React.FC<Props> = ({ onClose, onBack }) => {
  const openLink = (url: string) => {
    // In CEP environment, use window.open or cep.util.openURLInDefaultBrowser
    if (typeof window !== 'undefined' && window.open) {
      window.open(url, '_blank', 'noopener,noreferrer');
    } else {
      console.log('Open:', url);
    }
  };

  return (
    <div className={styles.wrapper}>
      <ModalHeader
        title="About SahAI Extension"
        onClose={onClose}
        onBack={onBack}
        showBackButton={true}
      />

      <main className={styles.body}>
        {/* Logo & version */}
        <section className={styles.hero}>
          <div className={styles.icon} aria-hidden="true">
            <StarIcon size={48} />
          </div>
          <h1>SahAI Extension</h1>
          <p className={styles.version}>Version 2.0.0</p>
        </section>

        {/* Features */}
        <section>
          <h2>Features</h2>
          <ul className={styles.list}>
            <li><b>Multi-Model Support</b> – GPT-4, Claude, Gemini, more</li>
            <li><b>Usage Analytics</b> – track tokens, cost, latency</li>
            <li><b>Adobe Integration</b> – AE, PR, PS, AI 2025+</li>
            <li><b>Zero-Config Setup</b> – encrypted local storage</li>
          </ul>
        </section>

        {/* Links */}
        <section>
          <h2>Useful Links</h2>
          <div className={styles.links}>
            <button
              className={styles.linkBtn}
              onClick={() => openLink('https://docs.sahai.ai')}
              aria-label="Open documentation in new window"
            >
              <InfoIcon size={16} />
              <span>Docs</span>
            </button>
            <button
              className={styles.linkBtn}
              onClick={() => openLink('https://github.com/sahai-ai/cep-extension')}
              aria-label="Open GitHub repository in new window"
            >
              <InfoIcon size={16} />
              <span>GitHub</span>
            </button>
            <button
              className={styles.linkBtn}
              onClick={() => openLink('https://sahai.ai/privacy')}
              aria-label="Open privacy policy in new window"
            >
              <InfoIcon size={16} />
              <span>Privacy</span>
            </button>
            <button
              className={styles.linkBtn}
              onClick={() => openLink('https://sahai.ai/terms')}
              aria-label="Open terms of service in new window"
            >
              <InfoIcon size={16} />
              <span>Terms</span>
            </button>
          </div>
        </section>

        {/* Footer */}
        <footer className={styles.footer}>
          <p>© 2025 SahAI Extension · All rights reserved</p>
          <small>This software is provided “as is”, without warranty of any kind.</small>
        </footer>
      </main>
    </div>
  );
};