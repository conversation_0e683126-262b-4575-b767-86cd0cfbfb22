/**
 * Adobe CEP About Modal – drop-in replacement
 * No blue close button, full CEP styling
 */

import React from 'react';
import { ModalHeader } from './ModalHeader';
import styles from './About.module.css';

interface Props {
  onClose: () => void;
  onBack?: () => void;
}

export const AboutModal: React.FC<Props> = ({ onClose, onBack }) => {
  const openLink = (url: string) => {
    // Replace with actual window.open when embedded
    console.log('Open:', url);
  };

  return (
    <div className={styles.wrapper}>
      <ModalHeader
        title="About SahAI Extension"
        onClose={onClose}
        onBack={onBack}
        showBackButton={true}
      />

      <main className={styles.body}>
        {/* Logo & version */}
        <section className={styles.hero}>
          <div className={styles.icon}>✨</div>
          <h1>SahAI Extension</h1>
          <p>Version 2.0.0</p>
        </section>

        {/* Features */}
        <section>
          <h2>Features</h2>
          <ul className={styles.list}>
            <li><b>Multi-Model Support</b> – GPT-4, <PERSON>, <PERSON>, more</li>
            <li><b>Usage Analytics</b> – track tokens, cost, latency</li>
            <li><b>Adobe Integration</b> – AE, PR, PS, AI 2025+</li>
            <li><b>Zero-Config Setup</b> – encrypted local storage</li>
          </ul>
        </section>

        {/* Links */}
        <section>
          <h2>Useful Links</h2>
          <div className={styles.links}>
            <button onClick={() => openLink('https://docs.sahai.ai')}>Docs</button>
            <button onClick={() => openLink('https://github.com/sahai-ai/cep-extension')}>GitHub</button>
            <button onClick={() => openLink('https://sahai.ai/privacy')}>Privacy</button>
            <button onClick={() => openLink('https://sahai.ai/terms')}>Terms</button>
          </div>
        </section>

        {/* Footer */}
        <footer className={styles.footer}>
          <p>© 2025 SahAI Extension · All rights reserved</p>
          <small>This software is provided “as is”, without warranty of any kind.</small>
        </footer>
      </main>
    </div>
  );
};