@import url("../../styles/adobe-tokens.css");
@import url("../../styles/typography.css");

.wrapper{display:flex;flex-direction:column;height:100%;font-family:var(--adobe-font-family);}
.body{flex:1;padding:var(--adobe-space-lg);overflow-y:auto;display:flex;flex-direction:column;gap:var(--adobe-space-xl);}

.hero{text-align:center;}
.icon{display:flex;justify-content:center;margin-bottom:var(--adobe-space-sm);color:var(--adobe-accent);}
.hero h1{margin:0 0 var(--adobe-space-xs);font-size:var(--adobe-font-size-3xl);font-weight:600;color:var(--adobe-text-primary);}
.version{margin:0;font-size:var(--adobe-font-size-sm);color:var(--adobe-text-secondary);font-weight:400;}

h2{margin:0 0 var(--adobe-space-sm);font-size:var(--adobe-font-size-lg);font-weight:600;text-transform:uppercase;letter-spacing:var(--adobe-letter-spacing-wider);color:var(--adobe-text-primary);}

.list{list-style:none;padding:0;margin:0;font-size:var(--adobe-font-size-md);line-height:var(--adobe-line-height-relaxed);color:var(--adobe-text-primary);}
.list li{margin-bottom:var(--adobe-space-xs);}

.links{display:grid;grid-template-columns:1fr 1fr;gap:var(--adobe-space-sm);}
.linkBtn{padding:var(--adobe-space-sm) var(--adobe-space-md);border:1px solid var(--adobe-border);background:var(--adobe-bg-secondary);color:var(--adobe-text-primary);font-size:var(--adobe-font-size-base);border-radius:var(--adobe-radius);cursor:pointer;transition:border-color .15s;display:flex;align-items:center;gap:var(--adobe-space-xs);font-family:var(--adobe-font-family);text-align:left;}
.linkBtn:hover{border-color:var(--adobe-accent);}
.linkBtn:focus{outline:2px solid var(--adobe-accent);outline-offset:-2px;}

.footer{margin-top:auto;text-align:center;font-size:var(--adobe-font-size-sm);color:var(--adobe-text-secondary);}
.footer p{margin:0 0 var(--adobe-space-xs);}
.footer small{display:block;opacity:.7;}