@import url("../../styles/adobe-tokens.css");

.wrapper{display:flex;flex-direction:column;height:100%;}
.body{flex:1;padding:16px;overflow-y:auto;display:flex;flex-direction:column;gap:20px;}

.hero{text-align:center;}
.icon{display:flex;justify-content:center;margin-bottom:8px;color:var(--adobe-accent);}
.hero h1{margin:0 0 4px;font-size:20px;font-weight:600;}
.version{margin:0;font-size:11px;color:var(--adobe-text-secondary);font-weight:400;}

h2{margin:0 0 8px;font-size:14px;font-weight:600;text-transform:uppercase;letter-spacing:.5px;}

.list{list-style:none;padding:0;margin:0;font-size:13px;line-height:1.5;}
.list li{margin-bottom:6px;}

.links{display:grid;grid-template-columns:1fr 1fr;gap:8px;}
.linkBtn{padding:8px 12px;border:1px solid var(--adobe-border);background:var(--adobe-bg-secondary);color:var(--adobe-text-primary);font-size:12px;border-radius:0;cursor:pointer;transition:border-color .15s;display:flex;align-items:center;gap:6px;font-family:var(--adobe-font-family);text-align:left;}
.linkBtn:hover{border-color:var(--adobe-accent);}
.linkBtn:focus{outline:2px solid var(--adobe-accent);outline-offset:-2px;}

.footer{margin-top:auto;text-align:center;font-size:11px;color:var(--adobe-text-secondary);}