/**
 * Modal Enhancement Test Component
 * Tests all the new UI components and enhancements
 */

import React, { useState } from 'react';
import { 
  Card, 
  SkeletonCard, 
  FAQItem, 
  Tooltip,
  StarIcon,
  InfoIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '../ui';
import styles from './ModalEnhancementTest.module.css';

export const ModalEnhancementTest: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);

  const toggleLoading = () => setIsLoading(!isLoading);

  const faqData = [
    {
      id: 'test-1',
      question: 'How do the new Card components work?',
      answer: 'The Card component supports multiple variants (default, kpi, status) with optional progress bars, tooltips, and loading states.'
    },
    {
      id: 'test-2', 
      question: 'What accessibility features are included?',
      answer: 'All components include ARIA attributes, keyboard navigation, screen reader support, and respect user motion preferences.'
    }
  ];

  return (
    <div className={styles.testContainer}>
      <h1 className={styles.title}>Modal Enhancement Test</h1>
      
      {/* Card Component Tests */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>Card Components</h2>
        
        <div className={styles.cardGrid}>
          {isLoading ? (
            <SkeletonCard variant="kpi" count={4} />
          ) : (
            <>
              <Card 
                label="Messages" 
                value="1,234" 
                variant="kpi"
                className={styles.testCard}
              />
              <Card 
                label="CPU Usage" 
                value="45%" 
                progress={45}
                progressColor="#00B341"
                variant="status"
                className={styles.testCard}
              />
              <Card 
                label="Memory" 
                value="2.1 GB" 
                progress={26}
                progressColor="#FFB900"
                variant="status"
                className={styles.testCard}
              />
              <Card 
                label="Status" 
                value="Online" 
                variant="default"
                className={styles.testCard}
              />
            </>
          )}
        </div>
        
        <button onClick={toggleLoading} className={styles.testButton}>
          {isLoading ? 'Hide Loading' : 'Show Loading'}
        </button>
      </section>

      {/* Tooltip Tests */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>Tooltip Components</h2>
        
        <div className={styles.tooltipGrid}>
          <Tooltip content="This tooltip appears on top" position="top">
            <button className={styles.testButton}>Hover for Top Tooltip</button>
          </Tooltip>
          
          <Tooltip content="This tooltip appears on the right" position="right">
            <button className={styles.testButton}>Hover for Right Tooltip</button>
          </Tooltip>
          
          <Tooltip content="This tooltip appears on the bottom" position="bottom">
            <button className={styles.testButton}>Hover for Bottom Tooltip</button>
          </Tooltip>
          
          <Tooltip content="This tooltip appears on the left" position="left">
            <button className={styles.testButton}>Hover for Left Tooltip</button>
          </Tooltip>
        </div>
      </section>

      {/* FAQ Component Tests */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>FAQ Components</h2>
        
        <div className={styles.faqList}>
          {faqData.map((item) => (
            <FAQItem
              key={item.id}
              question={item.question}
              answer={item.answer}
              questionId={item.id}
              className={styles.testFaq}
            />
          ))}
        </div>
      </section>

      {/* Icon Tests */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>Icon Components</h2>
        
        <div className={styles.iconGrid}>
          <div className={styles.iconTest}>
            <StarIcon size={24} />
            <span>StarIcon</span>
          </div>
          <div className={styles.iconTest}>
            <InfoIcon size={24} />
            <span>InfoIcon</span>
          </div>
          <div className={styles.iconTest}>
            <ChevronDownIcon size={24} />
            <span>ChevronDownIcon</span>
          </div>
          <div className={styles.iconTest}>
            <ChevronRightIcon size={24} />
            <span>ChevronRightIcon</span>
          </div>
        </div>
      </section>

      {/* Accessibility Tests */}
      <section className={styles.section}>
        <h2 className={styles.sectionTitle}>Accessibility Features</h2>
        
        <div className={styles.accessibilityTests}>
          <button 
            className={styles.accessibleButton}
            aria-label="Test button with proper ARIA label"
          >
            Accessible Button
          </button>
          
          <div 
            className={styles.accessibleItem}
            role="button"
            tabIndex={0}
            aria-current="false"
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                console.log('Keyboard activated!');
              }
            }}
          >
            Keyboard Navigable Item (try Tab + Enter/Space)
          </div>
          
          <div className={styles.srOnlyTest}>
            <span className={styles.srOnly}>This text is only for screen readers</span>
            <span>Visible text with hidden screen reader context</span>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ModalEnhancementTest;
