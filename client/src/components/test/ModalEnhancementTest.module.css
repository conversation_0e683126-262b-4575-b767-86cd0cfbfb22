@import url("../../styles/adobe-tokens.css");
@import url("../../styles/typography.css");
@import url("../../styles/animations.css");
@import url("../../styles/accessibility.css");

.testContainer {
  padding: var(--adobe-space-lg);
  background: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
  font-family: var(--adobe-font-family);
  max-width: 800px;
  margin: 0 auto;
}

.title {
  font-size: var(--adobe-font-size-3xl);
  font-weight: 600;
  color: var(--adobe-text-primary);
  text-align: center;
  margin-bottom: var(--adobe-space-2xl);
}

.section {
  margin-bottom: var(--adobe-space-3xl);
}

.sectionTitle {
  font-size: var(--adobe-font-size-xl);
  font-weight: 600;
  color: var(--adobe-text-primary);
  text-transform: uppercase;
  letter-spacing: var(--adobe-letter-spacing-wider);
  margin-bottom: var(--adobe-space-lg);
  border-bottom: 1px solid var(--adobe-border);
  padding-bottom: var(--adobe-space-sm);
}

/* Card Tests */
.cardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--adobe-space-md);
  margin-bottom: var(--adobe-space-lg);
}

.testCard {
  composes: hover-lift from '../../styles/animations.css';
}

/* Tooltip Tests */
.tooltipGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--adobe-space-lg);
  margin-bottom: var(--adobe-space-lg);
}

/* FAQ Tests */
.faqList {
  display: flex;
  flex-direction: column;
  gap: var(--adobe-space-sm);
}

.testFaq {
  /* Custom FAQ styling if needed */
}

/* Icon Tests */
.iconGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--adobe-space-lg);
  margin-bottom: var(--adobe-space-lg);
}

.iconTest {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--adobe-space-sm);
  padding: var(--adobe-space-md);
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  color: var(--adobe-text-primary);
}

.iconTest span {
  font-size: var(--adobe-font-size-sm);
  color: var(--adobe-text-secondary);
}

/* Accessibility Tests */
.accessibilityTests {
  display: flex;
  flex-direction: column;
  gap: var(--adobe-space-lg);
}

.accessibleButton {
  composes: accessible-button from '../../styles/accessibility.css';
}

.accessibleItem {
  composes: keyboard-navigable from '../../styles/accessibility.css';
  padding: var(--adobe-space-md);
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  color: var(--adobe-text-primary);
}

.srOnlyTest {
  padding: var(--adobe-space-md);
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  color: var(--adobe-text-primary);
}

.srOnly {
  composes: sr-only from '../../styles/accessibility.css';
}

/* Test Button */
.testButton {
  composes: accessible-button from '../../styles/accessibility.css';
  composes: hover-border from '../../styles/animations.css';
}

/* Responsive Design */
@media (max-width: 600px) {
  .cardGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .tooltipGrid {
    grid-template-columns: 1fr;
  }
  
  .iconGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 400px) {
  .cardGrid {
    grid-template-columns: 1fr;
  }
  
  .iconGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}
