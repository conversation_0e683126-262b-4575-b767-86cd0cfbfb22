/**
 * Accessibility Utilities for Adobe CEP Extension
 * WCAG 2.1 AA compliant styles and utilities
 */

@import url("./adobe-tokens.css");

/* Screen Reader Only Content */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.sr-only-focusable:focus {
  position: static !important;
  width: auto !important;
  height: auto !important;
  padding: inherit !important;
  margin: inherit !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
}

/* Focus Management */
.focus-visible {
  outline: 2px solid var(--adobe-accent) !important;
  outline-offset: 2px !important;
}

.focus-within {
  outline: 2px solid var(--adobe-accent);
  outline-offset: -2px;
}

.focus-trap {
  /* Ensures focus stays within modal */
  position: relative;
}

/* Skip Links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
  padding: 8px;
  text-decoration: none;
  border: 2px solid var(--adobe-accent);
  border-radius: 4px;
  z-index: 9999;
  font-size: var(--adobe-font-size-base);
  font-weight: 600;
}

.skip-link:focus {
  top: 6px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .high-contrast-border {
    border-width: 2px !important;
  }
  
  .high-contrast-text {
    font-weight: 600 !important;
  }
  
  .high-contrast-focus:focus {
    outline: 3px solid var(--adobe-accent) !important;
    outline-offset: 2px !important;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .respect-motion-preference {
    animation: none !important;
    transition: none !important;
  }
  
  .respect-motion-preference * {
    animation: none !important;
    transition: none !important;
  }
}

/* Color Blind Friendly */
.colorblind-safe {
  /* Use patterns or icons in addition to color */
}

.status-good::before {
  content: "✓ ";
  color: var(--adobe-success);
}

.status-warning::before {
  content: "⚠ ";
  color: #FFB900;
}

.status-error::before {
  content: "✗ ";
  color: var(--adobe-error);
}

/* Keyboard Navigation */
.keyboard-navigable {
  cursor: pointer;
}

.keyboard-navigable:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: -2px;
  background: var(--adobe-bg-hover);
}

.keyboard-navigable[aria-selected="true"] {
  background: var(--adobe-bg-hover);
  border-color: var(--adobe-accent);
}

/* ARIA Live Regions */
.live-region {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.live-region[aria-live="polite"] {
  /* Announces changes when user is idle */
}

.live-region[aria-live="assertive"] {
  /* Announces changes immediately */
}

/* Form Accessibility */
.form-field {
  position: relative;
}

.form-label {
  display: block;
  font-size: var(--adobe-font-size-sm);
  font-weight: 500;
  color: var(--adobe-text-primary);
  margin-bottom: 4px;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--adobe-border);
  background: var(--adobe-bg-secondary);
  color: var(--adobe-text-primary);
  font-family: var(--adobe-font-family);
  font-size: var(--adobe-font-size-base);
}

.form-input:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: -2px;
  border-color: var(--adobe-accent);
}

.form-input[aria-invalid="true"] {
  border-color: var(--adobe-error);
}

.form-error {
  color: var(--adobe-error);
  font-size: var(--adobe-font-size-sm);
  margin-top: 4px;
}

.form-help {
  color: var(--adobe-text-secondary);
  font-size: var(--adobe-font-size-sm);
  margin-top: 4px;
}

/* Button Accessibility */
.accessible-button {
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  color: var(--adobe-text-primary);
  padding: 8px 16px;
  font-family: var(--adobe-font-family);
  font-size: var(--adobe-font-size-base);
  cursor: pointer;
  transition: all 0.15s ease;
}

.accessible-button:hover {
  border-color: var(--adobe-accent);
  background: var(--adobe-bg-hover);
}

.accessible-button:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: -2px;
}

.accessible-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.accessible-button[aria-pressed="true"] {
  background: var(--adobe-accent);
  color: white;
}

/* Modal Accessibility */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-content {
  position: relative;
  background: var(--adobe-bg-primary);
  border: 1px solid var(--adobe-border);
  max-height: 90vh;
  overflow-y: auto;
}

.modal-content:focus {
  outline: none;
}

/* List Accessibility */
.accessible-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.accessible-list-item {
  padding: 8px 12px;
  border: 1px solid transparent;
  cursor: pointer;
}

.accessible-list-item:hover {
  background: var(--adobe-bg-hover);
  border-color: var(--adobe-border);
}

.accessible-list-item:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: -2px;
}

.accessible-list-item[aria-selected="true"] {
  background: var(--adobe-bg-hover);
  border-color: var(--adobe-accent);
}

/* Tooltip Accessibility */
.accessible-tooltip {
  position: relative;
}

.accessible-tooltip[aria-describedby] {
  cursor: help;
}

/* Progress Indicator Accessibility */
.progress-bar {
  background: var(--adobe-bg-tertiary);
  height: 4px;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--adobe-accent);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: var(--adobe-font-size-sm);
  color: var(--adobe-text-secondary);
  margin-top: 4px;
}

/* Error States */
.error-state {
  color: var(--adobe-error);
  border-color: var(--adobe-error);
}

.error-message {
  color: var(--adobe-error);
  font-size: var(--adobe-font-size-sm);
  margin-top: 4px;
}

.error-icon::before {
  content: "⚠ ";
  color: var(--adobe-error);
}
