/* Global Styles for SahAI CEP Extension V2 */
/* Following Adobe CEP Theme Standards from V1 */
@import './design-system.css';

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: var(--adobe-font-family);
  font-size: var(--font-size-base);
  line-height: 1.4;
  background: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
  margin: 0;
  line-height: 1.4;
}

/* Links */
a {
  color: var(--adobe-accent);
  text-decoration: none;
  transition: color var(--transition-normal);
}

a:hover {
  color: var(--adobe-accent-hover);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 1px;
}

/* Form elements */
input, textarea, select, button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

input, textarea, select {
  background: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  color: var(--adobe-text-primary);
  border-radius: var(--radius-md);
  padding: var(--space-2);
  outline: none;
  transition: border-color var(--transition-normal);
}

input:focus, textarea:focus, select:focus {
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

input:disabled, textarea:disabled, select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button {
  background: var(--adobe-accent);
  border: none;
  color: white;
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-3);
  cursor: pointer;
  transition: background-color var(--transition-normal);
  outline: none;
}

button:hover:not(:disabled) {
  background: var(--adobe-accent-hover);
}

button:focus {
  box-shadow: 0 0 0 2px var(--adobe-accent);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Adobe CEP-specific styles */
.adobe-panel {
  background: var(--adobe-bg-primary);
  color: var(--adobe-text-primary);
  border: 1px solid var(--adobe-border);
}

.adobe-header {
  background: var(--adobe-bg-secondary);
  border-bottom: 1px solid var(--adobe-border);
  padding: var(--space-2) var(--space-3);
}

.adobe-content {
  padding: var(--space-3);
}

/* Selection styles */
::selection {
  background: rgba(70, 160, 245, 0.3);
  color: var(--adobe-text-primary);
}

::-moz-selection {
  background: rgba(70, 160, 245, 0.3);
  color: var(--adobe-text-primary);
}
