/**
 * Animation Utilities for Adobe CEP Extension
 * Lightweight animations optimized for CEP environment
 */

@import url("./adobe-tokens.css");

/* Animation Durations */
:root {
  --adobe-duration-fast: 0.15s;
  --adobe-duration-normal: 0.2s;
  --adobe-duration-slow: 0.3s;
  --adobe-duration-slower: 0.5s;
  
  --adobe-easing-ease: ease;
  --adobe-easing-ease-in: ease-in;
  --adobe-easing-ease-out: ease-out;
  --adobe-easing-ease-in-out: ease-in-out;
}

/* Base Transitions */
.transition-fast {
  transition: all var(--adobe-duration-fast) var(--adobe-easing-ease);
}

.transition-normal {
  transition: all var(--adobe-duration-normal) var(--adobe-easing-ease);
}

.transition-slow {
  transition: all var(--adobe-duration-slow) var(--adobe-easing-ease);
}

/* Specific Property Transitions */
.transition-colors {
  transition: color var(--adobe-duration-fast) var(--adobe-easing-ease),
              background-color var(--adobe-duration-fast) var(--adobe-easing-ease),
              border-color var(--adobe-duration-fast) var(--adobe-easing-ease);
}

.transition-transform {
  transition: transform var(--adobe-duration-normal) var(--adobe-easing-ease);
}

.transition-opacity {
  transition: opacity var(--adobe-duration-normal) var(--adobe-easing-ease);
}

/* Hover Effects */
.hover-lift {
  transition: transform var(--adobe-duration-fast) var(--adobe-easing-ease),
              box-shadow var(--adobe-duration-fast) var(--adobe-easing-ease);
}

.hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.hover-scale {
  transition: transform var(--adobe-duration-fast) var(--adobe-easing-ease);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-border {
  transition: border-color var(--adobe-duration-fast) var(--adobe-easing-ease);
}

.hover-border:hover {
  border-color: var(--adobe-accent);
}

/* Loading Animations */
.pulse {
  animation: pulse var(--adobe-duration-slower) var(--adobe-easing-ease-in-out) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.shimmer {
  animation: shimmer 1.5s var(--adobe-easing-ease-in-out) infinite;
}

@keyframes shimmer {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Fade Animations */
.fade-in {
  animation: fadeIn var(--adobe-duration-normal) var(--adobe-easing-ease-out);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in-up {
  animation: fadeInUp var(--adobe-duration-normal) var(--adobe-easing-ease-out);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Expand/Collapse Animations */
.expand-vertical {
  overflow: hidden;
  transition: max-height var(--adobe-duration-slow) var(--adobe-easing-ease-out);
}

.expand-vertical.collapsed {
  max-height: 0;
}

.expand-vertical.expanded {
  max-height: 500px; /* Adjust based on content */
}

/* Focus Animations */
.focus-ring {
  transition: outline var(--adobe-duration-fast) var(--adobe-easing-ease);
}

.focus-ring:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: -2px;
}

.focus-ring:focus-visible {
  outline: 2px solid var(--adobe-accent);
  outline-offset: -2px;
}

/* Stagger Animations for Lists */
.stagger-fade-in > * {
  animation: fadeIn var(--adobe-duration-normal) var(--adobe-easing-ease-out);
}

.stagger-fade-in > *:nth-child(1) { animation-delay: 0ms; }
.stagger-fade-in > *:nth-child(2) { animation-delay: 50ms; }
.stagger-fade-in > *:nth-child(3) { animation-delay: 100ms; }
.stagger-fade-in > *:nth-child(4) { animation-delay: 150ms; }
.stagger-fade-in > *:nth-child(5) { animation-delay: 200ms; }
.stagger-fade-in > *:nth-child(6) { animation-delay: 250ms; }

/* Accessibility - Respect Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .hover-lift:hover,
  .hover-scale:hover {
    transform: none;
  }
  
  .expand-vertical {
    transition: none;
  }
  
  .expand-vertical.collapsed {
    max-height: 0;
  }
  
  .expand-vertical.expanded {
    max-height: none;
  }
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}
