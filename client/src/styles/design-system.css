/* SahAI Adobe CEP Theme System - Unified Design Variables */
/* Adobe CEP Theme Standards with complete variable mapping */

/* ===== DESIGN TOKENS ===== */
:root {
  /* Typography Scale - Adobe CEP Standard */
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --adobe-font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family: var(--adobe-font-family); /* Alias for compatibility */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing Scale - Adobe CEP Standard */
  --space-0: 0;
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;

  /* Border Radius - Adobe CEP Standard */
  --radius-none: 0;
  --radius-sm: 2px;
  --radius-md: 3px;
  --radius-lg: 4px;
  --radius-xl: 6px;
  --radius-full: 9999px;

  /* Shadows - Adobe CEP Standard */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --adobe-shadow: var(--shadow-md); /* Adobe standard shadow */

  /* Transitions - Adobe CEP Standard */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;

  /* Z-index Scale - Adobe CEP Standard */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ===== ADOBE CEP THEME VARIABLES ===== */
/* Light Theme - Applied when data-theme is NOT set or "light" */
body:not([data-theme]),
body[data-theme="light"] {
  /* Adobe CEP Light Theme Colors */
  --adobe-bg-primary: #f5f5f5;
  --adobe-bg-secondary: #e5e5e5;
  --adobe-bg-tertiary: #d5d5d5;
  --adobe-bg-hover: #e0e0e0;
  --adobe-bg-danger: #ffebee;

  --adobe-text-primary: #000000;
  --adobe-text-secondary: #666666;
  --adobe-text-muted: #999999;
  --adobe-text-tertiary: #999999;

  --adobe-border: #cccccc;
  --adobe-border-hover: #999999;

  --adobe-accent: #0066cc;
  --adobe-accent-hover: #0052a3;
  --adobe-accent-active: #004499;

  --adobe-success: #4caf50;
  --adobe-warning: #ff9800;
  --adobe-error: #f44336;
  --adobe-info: #2196f3;

  --adobe-color-primary: #0a84ff;
  --adobe-color-success: #4CAF50;
  --adobe-color-error: #F44336;

  --shadow-color: rgba(0, 0, 0, 0.1);

  /* Compatibility aliases for legacy --color-* variables */
  --color-bg-primary: var(--adobe-bg-primary);
  --color-bg-secondary: var(--adobe-bg-secondary);
  --color-bg-tertiary: var(--adobe-bg-tertiary);
  --color-text-primary: var(--adobe-text-primary);
  --color-text-secondary: var(--adobe-text-secondary);
  --color-text-muted: var(--adobe-text-muted);
  --color-border: var(--adobe-border);
  --color-border-hover: var(--adobe-border-hover);
  --color-accent: var(--adobe-accent);
  --color-accent-hover: var(--adobe-accent-hover);
  --color-success: var(--adobe-success);
  --color-warning: var(--adobe-warning);
  --color-error: var(--adobe-error);
  --color-info: var(--adobe-info);
}

/* Dark Theme - Applied when data-theme="dark" */
body[data-theme="dark"] {
  /* Adobe CEP Dark Theme Colors */
  --adobe-bg-primary: #2a2a2a;
  --adobe-bg-secondary: #323232;
  --adobe-bg-tertiary: #3a3a3a;
  --adobe-bg-hover: #4a4a4a;
  --adobe-bg-danger: #ff4444;

  --adobe-text-primary: #ffffff;
  --adobe-text-secondary: #cccccc;
  --adobe-text-muted: #999999;
  --adobe-text-tertiary: #999999;

  --adobe-border: #555555;
  --adobe-border-hover: #777777;

  --adobe-accent: #46a0f5;
  --adobe-accent-hover: #5ba7f7;
  --adobe-accent-active: #3d8bdb;

  --adobe-success: #4caf50;
  --adobe-warning: #ff9800;
  --adobe-error: #f44336;
  --adobe-info: #2196f3;

  --adobe-color-primary: #0a84ff;
  --adobe-color-success: #4CAF50;
  --adobe-color-error: #F44336;

  --shadow-color: rgba(0, 0, 0, 0.3);

  /* Compatibility aliases for legacy --color-* variables */
  --color-bg-primary: var(--adobe-bg-primary);
  --color-bg-secondary: var(--adobe-bg-secondary);
  --color-bg-tertiary: var(--adobe-bg-tertiary);
  --color-text-primary: var(--adobe-text-primary);
  --color-text-secondary: var(--adobe-text-secondary);
  --color-text-muted: var(--adobe-text-muted);
  --color-border: var(--adobe-border);
  --color-border-hover: var(--adobe-border-hover);
  --color-accent: var(--adobe-accent);
  --color-accent-hover: var(--adobe-accent-hover);
  --color-success: var(--adobe-success);
  --color-warning: var(--adobe-warning);
  --color-error: var(--adobe-error);
  --color-info: var(--adobe-info);
}

/* ===== RESET & BASE ===== */
* {
  box-sizing: border-box;
}

/* ===== UTILITY CLASSES ===== */

/* Layout */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-1 { flex: 1; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }
.flex-wrap { flex-wrap: wrap; }
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

/* Spacing */
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }

.p-0 { padding: var(--space-0); }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }

.px-1 { padding-left: var(--space-1); padding-right: var(--space-1); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }

.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }

.m-0 { margin: var(--space-0); }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }

.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }

.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }

.ml-1 { margin-left: var(--space-1); }
.ml-2 { margin-left: var(--space-2); }
.ml-3 { margin-left: var(--space-3); }
.ml-4 { margin-left: var(--space-4); }

.mr-1 { margin-right: var(--space-1); }
.mr-2 { margin-right: var(--space-2); }
.mr-3 { margin-right: var(--space-3); }
.mr-4 { margin-right: var(--space-4); }

/* Sizing */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.min-h-0 { min-height: 0; }
.min-w-0 { min-width: 0; }

/* Typography */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-primary { color: var(--adobe-text-primary); }
.text-secondary { color: var(--adobe-text-secondary); }
.text-muted { color: var(--adobe-text-muted); }
.text-success { color: var(--adobe-success); }
.text-warning { color: var(--adobe-warning); }
.text-error { color: var(--adobe-error); }

/* Backgrounds */
.bg-primary { background-color: var(--adobe-bg-primary); }
.bg-secondary { background-color: var(--adobe-bg-secondary); }
.bg-tertiary { background-color: var(--adobe-bg-tertiary); }

/* Borders */
.border { border: 1px solid var(--adobe-border); }
.border-t { border-top: 1px solid var(--adobe-border); }
.border-b { border-bottom: 1px solid var(--adobe-border); }
.border-l { border-left: 1px solid var(--adobe-border); }
.border-r { border-right: 1px solid var(--adobe-border); }

.rounded { border-radius: var(--radius-md); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-lg { border-radius: var(--radius-lg); }

/* Shadows */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }

/* ===== COMPONENT PATTERNS ===== */

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-2) var(--space-3);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  cursor: pointer;
  transition: all var(--transition-normal);
  outline: none;
  white-space: nowrap;
}

.btn:focus {
  box-shadow: 0 0 0 2px var(--adobe-accent);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--adobe-accent);
  color: white;
  border-color: var(--adobe-accent);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--adobe-accent-hover);
  border-color: var(--adobe-accent-hover);
}

.btn-secondary {
  background-color: transparent;
  color: var(--adobe-text-primary);
  border-color: var(--adobe-border);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--adobe-bg-secondary);
  border-color: var(--adobe-border-hover);
}

.btn-sm {
  padding: var(--space-1) var(--space-2);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-lg);
}

/* Forms */
.form-group {
  margin-bottom: var(--space-4);
}

.form-label {
  display: block;
  margin-bottom: var(--space-1);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--adobe-text-primary);
}

.form-input {
  width: 100%;
  padding: var(--space-2);
  border: 1px solid var(--adobe-border);
  border-radius: var(--radius-md);
  background-color: var(--adobe-bg-secondary);
  color: var(--adobe-text-primary);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-normal);
}

.form-input:focus {
  outline: none;
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

.form-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-select {
  width: 100%;
  padding: var(--space-2);
  border: 1px solid var(--adobe-border);
  border-radius: var(--radius-md);
  background-color: var(--adobe-bg-secondary);
  color: var(--adobe-text-primary);
  font-size: var(--font-size-base);
  cursor: pointer;
}

.form-select:focus {
  outline: none;
  border-color: var(--adobe-accent);
  box-shadow: 0 0 0 1px var(--adobe-accent);
}

/* Cards */
.card {
  background-color: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
}

.card-header {
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--adobe-border);
  margin-bottom: var(--space-3);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--adobe-text-primary);
  margin: 0;
}

.card-body {
  flex: 1;
}

/* Modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.modal-container {
  background-color: var(--adobe-bg-secondary);
  border: 1px solid var(--adobe-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  border-bottom: 1px solid var(--adobe-border);
}

.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--adobe-text-primary);
  margin: 0;
}

.modal-body {
  padding: var(--space-4);
  overflow-y: auto;
  max-height: calc(90vh - 120px);
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--space-2);
  padding: var(--space-4);
  border-top: 1px solid var(--adobe-border);
}

/* ===== SCROLLBARS - Adobe CEP Standard ===== */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: var(--adobe-border) var(--adobe-bg-primary);
}

.scrollbar-thin::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: var(--adobe-bg-primary);
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: var(--adobe-border);
  border-radius: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: var(--adobe-text-secondary);
}

/* ===== ACCESSIBILITY ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible:focus-visible {
  outline: 2px solid var(--adobe-accent);
  outline-offset: 1px;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 400px) {
  .responsive-padding {
    padding: var(--space-2);
  }
  
  .responsive-text {
    font-size: var(--font-size-sm);
  }
}

/* ===== HIGH CONTRAST MODE - Adobe CEP Standard ===== */
@media (prefers-contrast: high) {
  :root {
    --adobe-border: #000000;
    --adobe-accent: #0000ff;
    --color-border: #000000; /* Legacy alias */
    --color-accent: #0000ff; /* Legacy alias */
  }

  body:not([data-theme]),
  body[data-theme="light"] {
    --adobe-text-primary: #000000;
    --adobe-text-secondary: #333333;
    --adobe-bg-primary: #ffffff;
    --adobe-bg-secondary: #f5f5f5;
    /* Legacy aliases */
    --color-text-primary: #000000;
    --color-text-secondary: #333333;
    --color-bg-primary: #ffffff;
    --color-bg-secondary: #f5f5f5;
  }

  body[data-theme="dark"] {
    --adobe-text-primary: #ffffff;
    --adobe-text-secondary: #cccccc;
    --adobe-bg-primary: #000000;
    --adobe-bg-secondary: #1a1a1a;
    /* Legacy aliases */
    --color-text-primary: #ffffff;
    --color-text-secondary: #cccccc;
    --color-bg-primary: #000000;
    --color-bg-secondary: #1a1a1a;
  }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
