:root {
  /* Adobe CEP core palette */
  --adobe-bg-primary: #181818;
  --adobe-bg-secondary: #252525;
  --adobe-bg-tertiary: #2E2E2E;
  --adobe-bg-hover: #3A3A3A;
  --adobe-border: #444;
  --adobe-text-primary: #E6E6E6;
  --adobe-text-secondary: #B3B3B3;
  --adobe-accent: #0A84FF;
  --adobe-accent-hover: #0060DF;
  --adobe-success: #00B341;
  --adobe-error: #FF453A;
  --adobe-font-family: "Source Sans Pro", "Segoe UI", sans-serif;
  --adobe-radius: 0; /* Adobe CEP panels are 100% rectangular */

  /* Typography Scale */
  --adobe-font-size-xs: 10px;
  --adobe-font-size-sm: 11px;
  --adobe-font-size-base: 12px;
  --adobe-font-size-md: 13px;
  --adobe-font-size-lg: 14px;
  --adobe-font-size-xl: 16px;
  --adobe-font-size-2xl: 18px;
  --adobe-font-size-3xl: 20px;
  --adobe-font-size-4xl: 24px;

  /* Line Heights */
  --adobe-line-height-tight: 1.2;
  --adobe-line-height-normal: 1.4;
  --adobe-line-height-relaxed: 1.6;

  /* Spacing Scale */
  --adobe-space-xs: 4px;
  --adobe-space-sm: 8px;
  --adobe-space-md: 12px;
  --adobe-space-lg: 16px;
  --adobe-space-xl: 20px;
  --adobe-space-2xl: 24px;
  --adobe-space-3xl: 32px;

  /* Letter Spacing */
  --adobe-letter-spacing-tight: -0.025em;
  --adobe-letter-spacing-normal: 0;
  --adobe-letter-spacing-wide: 0.025em;
  --adobe-letter-spacing-wider: 0.05em;
  --adobe-letter-spacing-widest: 0.1em;
}
