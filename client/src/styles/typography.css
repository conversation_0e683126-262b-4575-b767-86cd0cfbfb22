/**
 * Typography Utilities for Adobe CEP Extension
 * Consistent typography classes across all modals
 */

@import url("./adobe-tokens.css");

/* Base Typography */
.modal-title {
  font-family: var(--adobe-font-family);
  font-size: var(--adobe-font-size-lg);
  font-weight: 600;
  color: var(--adobe-text-primary);
  text-transform: uppercase;
  letter-spacing: var(--adobe-letter-spacing-wider);
  margin: 0 0 var(--adobe-space-sm);
  line-height: var(--adobe-line-height-tight);
}

.modal-subtitle {
  font-family: var(--adobe-font-family);
  font-size: var(--adobe-font-size-md);
  font-weight: 500;
  color: var(--adobe-text-primary);
  margin: 0 0 var(--adobe-space-sm);
  line-height: var(--adobe-line-height-normal);
}

.modal-body-text {
  font-family: var(--adobe-font-family);
  font-size: var(--adobe-font-size-base);
  color: var(--adobe-text-primary);
  line-height: var(--adobe-line-height-normal);
  margin: 0 0 var(--adobe-space-sm);
}

.modal-caption {
  font-family: var(--adobe-font-family);
  font-size: var(--adobe-font-size-sm);
  color: var(--adobe-text-secondary);
  line-height: var(--adobe-line-height-normal);
  margin: 0;
}

.modal-label {
  font-family: var(--adobe-font-family);
  font-size: var(--adobe-font-size-sm);
  font-weight: 500;
  color: var(--adobe-text-secondary);
  text-transform: uppercase;
  letter-spacing: var(--adobe-letter-spacing-wider);
  margin: 0 0 var(--adobe-space-xs);
  line-height: var(--adobe-line-height-tight);
}

/* Spacing Utilities */
.modal-section {
  margin-bottom: var(--adobe-space-xl);
}

.modal-section:last-child {
  margin-bottom: 0;
}

.modal-grid {
  display: grid;
  gap: var(--adobe-space-md);
}

.modal-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.modal-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.modal-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.modal-grid-auto {
  grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
}

/* Responsive Typography */
@media (max-width: 400px) {
  .modal-title {
    font-size: var(--adobe-font-size-base);
  }
  
  .modal-subtitle {
    font-size: var(--adobe-font-size-base);
  }
  
  .modal-body-text {
    font-size: var(--adobe-font-size-sm);
  }
  
  .modal-grid-4,
  .modal-grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .modal-grid-2 {
    grid-template-columns: 1fr;
  }
}

/* Focus and Interaction States */
.modal-interactive {
  transition: all 0.15s ease;
}

.modal-interactive:hover {
  transform: translateY(-1px);
}

.modal-interactive:focus {
  outline: 2px solid var(--adobe-accent);
  outline-offset: -2px;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .modal-title,
  .modal-subtitle {
    font-weight: 700;
  }
  
  .modal-label {
    font-weight: 600;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .modal-interactive {
    transition: none;
  }
  
  .modal-interactive:hover {
    transform: none;
  }
}
